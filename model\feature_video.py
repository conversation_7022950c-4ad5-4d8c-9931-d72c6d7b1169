
import os
import cv2
from model.feature_img import ImageProcessor

class VideoProcessor:
    def __init__(self, image_processor=None):
        self.image_processor = image_processor


    def process_video(self, video_path, output_dir):
        os.makedirs(output_dir, exist_ok=True)
        if self.image_processor is None:
            self.image_processor = ImageProcessor(output_dir)
        else:
            self.image_processor = self.image_processor

        cap = cv2.VideoCapture(video_path)
        if not cap.isOpened():
            print(f"无法打开视频: {video_path}")
            return
        frame_idx = 0
        base_name = os.path.splitext(os.path.basename(video_path))[0]
        fps = cap.get(cv2.CAP_PROP_FPS)
        width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
        height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
        fourcc = cv2.VideoWriter_fourcc(*'mp4v')
        output_video_path = os.path.join(output_dir, f"{base_name}_processed.mp4")
        out = cv2.VideoWriter(output_video_path, fourcc, fps, (width, height))
        while True:
            ret, frame = cap.read()
            if not ret:
                break
            # 临时帧
            temp_img_path = os.path.join(output_dir, f"{base_name}_frame{frame_idx}.jpg")
            cv2.imwrite(temp_img_path, frame)
            # 处理帧，保存
            self.image_processor.process_image(temp_img_path)
            processed_img_path = os.path.join(
                output_dir, 
                f"{base_name}_frame{frame_idx}_{base_name}_frame{frame_idx}_equalized.jpg"
            )
            # 读取处理后的帧（以直方图均衡化结果为例）
            processed_frame = cv2.imread(processed_img_path)
            if processed_frame is not None:
                out.write(processed_frame)
            frame_idx += 1
            os.remove(temp_img_path)  # 删除临时帧
        cap.release()
        out.release()
        print(f"处理后的视频已保存到: {output_video_path}")

