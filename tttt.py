import sys
import os
import random
import numpy as np
import cv2
import pywt
from PyQt5.QtWidgets import (QApplication, QMainWindow, QTabWidget, QWidget, QVBoxLayout, 
                            QHBoxLayout, QLabel, QPushButton, QFileDialog, QSlider, 
                            QGroupBox, QGridLayout, QTextEdit, QComboBox, QSplitter)
from PyQt5.QtGui import QPixmap, QImage, QFont
from PyQt5.QtCore import Qt, QThread, pyqtSignal
from ultralytics import YOLO
from typing import List, Dict, Tuple, Optional

# 确保中文显示正常
# QFontDatabase.addApplicationFont("simhei.ttf")  # 如果系统中没有SimHei字体，需要提供字体文件

class FeatureExtractor:
    """特征提取器，用于从不同类型的图像中提取特征"""
    
    @staticmethod
    def extract_visible_features(image: np.ndarray) -> Dict:
        """从可见光图像中提取特征"""
        # 转换为灰度图
        gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
        
        # 计算亮度统计
        mean_brightness = np.mean(gray)
        std_brightness = np.std(gray)
        
        # 边缘检测
        edges = cv2.Canny(gray, 50, 150)
        edge_density = np.count_nonzero(edges) / (edges.shape[0] * edges.shape[1])
        
        # 颜色特征
        hsv = cv2.cvtColor(image, cv2.COLOR_BGR2HSV)
        mean_hue = np.mean(hsv[:, :, 0])
        mean_saturation = np.mean(hsv[:, :, 1])
        
        # 频域特征
        f = np.fft.fft2(gray)
        fshift = np.fft.fftshift(f)
        magnitude_spectrum = 20*np.log(np.abs(fshift))
        mean_freq = np.mean(magnitude_spectrum)
        
        # 小波域特征
        coeffs2 = pywt.dwt2(gray, 'bior1.3')
        LL, (LH, HL, HH) = coeffs2
        mean_wavelet = np.mean(LL)
        
        return {
            'mean_brightness': mean_brightness,
            'std_brightness': std_brightness,
            'edge_density': edge_density,
            'mean_hue': mean_hue,
            'mean_saturation': mean_saturation,
            'mean_freq': mean_freq,
            'mean_wavelet': mean_wavelet,
            'feature_vector': [mean_brightness, std_brightness, edge_density, mean_hue, mean_saturation, mean_freq, mean_wavelet]
        }
    
    @staticmethod
    def extract_thermal_features(image: np.ndarray) -> Dict:
        """从红外图像中提取特征"""
        # 假设输入的红外图像是单通道的，如果是三通道则转换为单通道
        if len(image.shape) == 3:
            gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
        else:
            gray = image
        
        # 计算温度统计（这里用像素值表示相对温度）
        mean_temp = np.mean(gray)
        max_temp = np.max(gray)
        min_temp = np.min(gray)
        temp_variance = np.var(gray)
        
        # 热点检测
        _, binary = cv2.threshold(gray, mean_temp + np.std(gray), 255, cv2.THRESH_BINARY)
        hotspots_ratio = np.count_nonzero(binary) / (binary.shape[0] * binary.shape[1])
        
        # 频域特征
        f = np.fft.fft2(gray)
        fshift = np.fft.fftshift(f)
        magnitude_spectrum = 20*np.log(np.abs(fshift))
        mean_freq = np.mean(magnitude_spectrum)
        
        # 小波域特征
        coeffs2 = pywt.dwt2(gray, 'bior1.3')
        LL, (LH, HL, HH) = coeffs2
        mean_wavelet = np.mean(LL)
        
        return {
            'mean_temp': mean_temp,
            'max_temp': max_temp,
            'min_temp': min_temp,
            'temp_variance': temp_variance,
            'hotspots_ratio': hotspots_ratio,
            'mean_freq': mean_freq,
            'mean_wavelet': mean_wavelet,
            'feature_vector': [mean_temp, max_temp, min_temp, temp_variance, hotspots_ratio, mean_freq, mean_wavelet]
        }
    
    @staticmethod
    def extract_sar_features(image: np.ndarray) -> Dict:
        """从SAR图像中提取特征"""
        # 假设输入的SAR图像是单通道的
        if len(image.shape) == 3:
            gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
        else:
            gray = image
        
        # 计算后向散射统计
        mean_backscatter = np.mean(gray)
        std_backscatter = np.std(gray)
        
        # 纹理特征（简化版本，替代GLCM）
        # 使用梯度计算纹理特征
        gradient_x = cv2.Sobel(gray, cv2.CV_64F, 1, 0, ksize=3)
        gradient_y = cv2.Sobel(gray, cv2.CV_64F, 0, 1, ksize=3)
        gradient_magnitude = np.sqrt(gradient_x**2 + gradient_y**2)

        contrast = np.std(gradient_magnitude)
        homogeneity = 1.0 / (1.0 + np.var(gray))
        energy = np.sum(gray**2) / (gray.shape[0] * gray.shape[1])
        correlation = np.corrcoef(gradient_x.flatten(), gradient_y.flatten())[0,1]
        if np.isnan(correlation):
            correlation = 0.0
        
        # 频域特征
        f = np.fft.fft2(gray)
        fshift = np.fft.fftshift(f)
        magnitude_spectrum = 20*np.log(np.abs(fshift))
        mean_freq = np.mean(magnitude_spectrum)
        
        # 小波域特征
        coeffs2 = pywt.dwt2(gray, 'bior1.3')
        LL, (LH, HL, HH) = coeffs2
        mean_wavelet = np.mean(LL)
        
        return {
            'mean_backscatter': mean_backscatter,
            'std_backscatter': std_backscatter,
            'contrast': contrast,
            'homogeneity': homogeneity,
            'energy': energy,
            'correlation': correlation,
            'mean_freq': mean_freq,
            'mean_wavelet': mean_wavelet,
            'feature_vector': [mean_backscatter, std_backscatter, contrast, homogeneity, energy, correlation, mean_freq, mean_wavelet]
        }

class SceneClassifier:
    """场景分类器，根据多模态特征判断是白天还是黑夜"""
    
    @staticmethod
    def classify_scene(visible_features: Dict, thermal_features: Dict, sar_features: Dict) -> Tuple[str, str]:
        """
        分类场景为白天或黑夜
        
        返回:
            分类结果 (str): "白天" 或 "黑夜"
            判别依据 (str): 用于解释分类结果的特征描述
        """
        # 计算各模态的可识别权重
        visible_weight = SceneClassifier._calculate_visible_weight(visible_features)
        thermal_weight = SceneClassifier._calculate_thermal_weight(thermal_features)
        sar_weight = SceneClassifier._calculate_sar_weight(sar_features)
        
        # 归一化权重
        total_weight = visible_weight + thermal_weight + sar_weight
        visible_weight /= total_weight
        thermal_weight /= total_weight
        sar_weight /= total_weight
        
        # 基于特征的昼夜判断
        # 可见光图像亮度高、饱和度高、边缘密度高，倾向于白天
        visible_day_score = visible_features['mean_brightness'] / 255.0 * 0.4 + \
                           visible_features['mean_saturation'] / 255.0 * 0.3 + \
                           visible_features['edge_density'] * 0.3
        
        # 红外图像温度方差大、热点多，倾向于白天
        thermal_day_score = thermal_features['temp_variance'] / 10000.0 * 0.6 + \
                           thermal_features['hotspots_ratio'] * 0.4
        
        # SAR图像后向散射标准差大，倾向于白天（更多的人造结构）
        sar_day_score = sar_features['std_backscatter'] / 50.0 * 0.5 + \
                       (sar_features['contrast'] / 1000.0) * 0.5
        
        # 综合得分
        final_score = visible_day_score * visible_weight + \
                     thermal_day_score * thermal_weight + \
                     sar_day_score * sar_weight
        
        # 分类结果
        result = "白天" if final_score > 0.5 else "黑夜"
        
        # 构建判别依据
        explanation = f"可见光权重: {visible_weight:.2f}, 红外权重: {thermal_weight:.2f}, SAR权重: {sar_weight:.2f}\n"
        explanation += f"可见光特征: 亮度={visible_features['mean_brightness']:.1f}, 饱和度={visible_features['mean_saturation']:.1f}, 边缘密度={visible_features['edge_density']:.3f}\n"
        explanation += f"红外特征: 温度方差={thermal_features['temp_variance']:.1f}, 热点比例={thermal_features['hotspots_ratio']:.3f}\n"
        explanation += f"SAR特征: 后向散射标准差={sar_features['std_backscatter']:.1f}, 对比度={sar_features['contrast']:.1f}\n"
        explanation += f"昼夜得分: {final_score:.3f} (阈值=0.5)"
        explanation += f"因为可见光亮度{visible_features['mean_brightness']:.1f}和红外温度方差{thermal_features['temp_variance']:.1f}存在差异，所以判断为{result}"
        
        return result, explanation
    
    @staticmethod
    def _calculate_visible_weight(features: Dict) -> float:
        """计算可见光图像的可识别权重"""
        # 亮度适中时权重高，过亮或过暗时权重低
        brightness_factor = 1.0 - abs(features['mean_brightness'] - 127.5) / 127.5
        # 边缘丰富时权重高
        edge_factor = min(features['edge_density'] * 5, 1.0)
        return (brightness_factor * 0.6 + edge_factor * 0.4) * 1.2  # 可见光权重放大
    
    @staticmethod
    def _calculate_thermal_weight(features: Dict) -> float:
        """计算红外图像的可识别权重"""
        # 温度方差大时权重高
        variance_factor = min(features['temp_variance'] / 10000.0, 1.0)
        # 热点多时权重高
        hotspot_factor = min(features['hotspots_ratio'] * 10, 1.0)
        return variance_factor * 0.6 + hotspot_factor * 0.4
    
    @staticmethod
    def _calculate_sar_weight(features: Dict) -> float:
        """计算SAR图像的可识别权重"""
        # 后向散射标准差大时权重高
        std_factor = min(features['std_backscatter'] / 50.0, 1.0)
        # 纹理对比度高时权重高
        contrast_factor = min(features['contrast'] / 1000.0, 1.0)
        return std_factor * 0.5 + contrast_factor * 0.5

class ObjectDetector:
    """目标检测器，使用YOLO模型检测图像中的物体"""
    
    def __init__(self, model_path: str = "yolov8n.pt"):
        """初始化检测器"""
        try:
            print(f"正在加载YOLO模型: {model_path}")
            self.model = YOLO(model_path)
            self.class_names = self.model.names
            print("YOLO模型加载成功")
        except Exception as e:
            print(f"YOLO模型加载失败: {e}")
            self.model = None
            self.class_names = {}
    
    def detect(self, image: np.ndarray, add_error: bool = False, error_level: int = 50) -> List[Dict]:
        """
        检测图像中的物体

        Args:
            image: 输入图像
            add_error: 是否添加检测误差
            error_level: 误差级别

        Returns:
            检测结果列表，每个结果包含类别ID、类别名称、置信度和边界框坐标
        """
        if self.model is None:
            print("YOLO模型未加载，返回空检测结果")
            return []

        results = self.model(image)
        detections = []
        
        for box in results[0].boxes:
            class_id = int(box.cls)
            confidence = float(box.conf)
            x1, y1, x2, y2 = box.xyxy[0].tolist()
            
            # 添加随机误差
            if add_error:
                x1 += random.randint(-error_level, error_level)
                y1 += random.randint(-error_level, error_level)
                x2 += random.randint(-error_level, error_level)
                y2 += random.randint(-error_level, error_level)
                
                # 确保边界框有效
                x1 = max(0, x1)
                y1 = max(0, y1)
                x2 = min(image.shape[1], x2)
                y2 = min(image.shape[0], y2)
            
            detections.append({
                'class_id': class_id,
                'class_name': self.class_names[class_id],
                'confidence': confidence,
                'bbox': [x1, y1, x2, y2]
            })
        
        return detections
    
    def correct_detections(self, detections: List[Dict]) -> List[Dict]:
        """
        校正检测结果，减少误差
        
        Args:
            detections: 包含误差的检测结果
            
        Returns:
            校正后的检测结果
        """
        # 简单的校正策略：计算同类物体的平均位置
        class_detections = {}
        for det in detections:
            class_name = det['class_name']
            if class_name not in class_detections:
                class_detections[class_name] = []
            class_detections[class_name].append(det)
        
        corrected_detections = []
        for class_name, dets in class_detections.items():
            if len(dets) > 1:
                # 同类物体有多个检测结果，合并它们
                boxes = np.array([det['bbox'] for det in dets])
                
                # 计算平均边界框
                avg_x1 = np.mean(boxes[:, 0])
                avg_y1 = np.mean(boxes[:, 1])
                avg_x2 = np.mean(boxes[:, 2])
                avg_y2 = np.mean(boxes[:, 3])
                
                # 计算平均置信度
                confidences = np.array([det['confidence'] for det in dets])
                avg_confidence = np.mean(confidences)
                
                corrected_detections.append({
                    'class_id': dets[0]['class_id'],
                    'class_name': class_name,
                    'confidence': avg_confidence,
                    'bbox': [avg_x1, avg_y1, avg_x2, avg_y2]
                })
            else:
                # 只有一个检测结果，保持不变
                corrected_detections.extend(dets)
        
        return corrected_detections

class DetectionWorker(QThread):
    """用于在后台执行目标检测的工作线程"""
    detection_complete = pyqtSignal(object, object, object)
    
    def __init__(self, detector, image, error_level):
        super().__init__()
        self.detector = detector
        self.image = image
        self.error_level = error_level
        
    def run(self):
        # 执行带误差的检测
        detections_with_error = self.detector.detect(self.image, add_error=True, error_level=self.error_level)
        
        # 校正检测结果
        corrected_detections = self.detector.correct_detections(detections_with_error)
        
        # 发送结果
        self.detection_complete.emit(detections_with_error, corrected_detections, self.image)

class KnowledgeBase:
    """知识库，存储各类别的本体知识"""
    
    def __init__(self):
        self.knowledge = {
            'person': '人是两足直立的哺乳动物，通常具有智能和社会性。在监控场景中，人是最常见的目标之一。',
            'bicycle': '自行车是一种两轮交通工具，通常由人力驱动。在城市和乡村环境中都很常见。',
            'car': '汽车是一种四轮机动车辆，通常用于运输乘客和货物。常见于道路和停车场。',
            'motorcycle': '摩托车是一种两轮机动车辆，速度快，机动性强。常见于城市和高速公路。',
            'airplane': '飞机是一种空中交通工具，通常用于长途旅行和货物运输。常见于机场和天空。',
            'bus': '巴士是一种大型公共交通工具，用于运输大量乘客。常见于城市公交线路。',
            'train': '火车是一种铁路交通工具，用于长途和短途运输。常见于火车站和铁路轨道。',
            'truck': '卡车是一种大型载货车辆，用于运输货物。常见于货运和物流场景。',
            'boat': '船是一种水上交通工具，用于在河流、湖泊和海洋中航行。',
            'traffic light': '交通灯是一种交通控制设备，用于指挥车辆和行人的通行。',
            'fire hydrant': '消防栓是一种消防设备，用于提供灭火用水。常见于城市街道。',
            'stop sign': '停车标志是一种交通标志，指示车辆必须停车。',
            'parking meter': '停车计时器是一种收费设备，用于计时停车。常见于城市停车场。',
            'bench': '长椅是一种供人休息的座位，常见于公园、广场和街道。',
            'bird': '鸟是一种有羽毛的脊椎动物，通常会飞行。常见于自然环境中。',
            'cat': '猫是一种家养宠物，属于猫科动物。性格独立，喜欢玩耍。',
            'dog': '狗是一种家养宠物，属于犬科动物。通常忠诚，可训练性强。',
            'horse': '马是一种大型哺乳动物，通常用于骑乘和运输。',
            'sheep': '羊是一种家养动物，主要用于生产羊毛和肉类。',
            'cow': '牛是一种家养动物，主要用于生产牛奶和肉类。',
            'elephant': '大象是一种大型哺乳动物，是陆地上最大的动物。具有长鼻子和大耳朵。',
            'bear': '熊是一种大型哺乳动物，通常生活在森林中。体型庞大，力量强大。',
            'zebra': '斑马是一种非洲哺乳动物，具有独特的黑白条纹。',
            'giraffe': '长颈鹿是一种非洲哺乳动物，以其超长的脖子和高大的身材而闻名。',
            'backpack': '背包是一种用于携带物品的袋子，通常背在背上。常见于旅行和上学。',
            'umbrella': '雨伞是一种防雨工具，由伞骨和伞面组成。',
            'handbag': '手提包是一种用于携带个人物品的袋子，通常由女性使用。',
            'tie': '领带是一种男性服饰，通常系在衬衫领口。',
            'suitcase': '行李箱是一种用于旅行的大型箱子，用于存放衣物和物品。',
            'frisbee': '飞盘是一种投掷玩具，通常由塑料制成。',
            'skis': '滑雪板是一种冬季运动装备，用于在雪地上滑行。',
            'snowboard': '滑雪板是一种冬季运动装备，用于在雪地上滑行，通常比滑雪板更宽。',
            'sports ball': '运动球是一种球类运动器材，如足球、篮球、网球等。',
            'kite': '风筝是一种玩具，通过风力在空中飞行。',
            'baseball bat': '棒球棒是一种棒球运动器材，用于击打棒球。',
            'baseball glove': '棒球手套是一种棒球运动器材，用于接住棒球。',
            'skateboard': '滑板是一种运动器材，用于在地面上滑行。',
            'surfboard': '冲浪板是一种水上运动器材，用于在海浪上滑行。',
            'tennis racket': '网球拍是一种网球运动器材，用于击打网球。',
            'bottle': '瓶子是一种容器，通常用于存放液体。',
            'wine glass': '葡萄酒杯是一种玻璃容器，用于盛装葡萄酒。',
            'cup': '杯子是一种容器，通常用于盛装液体，如茶、咖啡等。',
            'fork': '叉子是一种餐具，用于叉取食物。',
            'knife': '刀是一种餐具，用于切割食物。',
            'spoon': '勺子是一种餐具，用于舀取食物。',
            'bowl': '碗是一种容器，通常用于盛装食物。',
            'banana': '香蕉是一种水果，黄色外皮，柔软果肉。',
            'apple': '苹果是一种水果，通常有红色、绿色或黄色外皮。',
            'sandwich': '三明治是一种食品，通常由两片面包夹着馅料组成。',
            'orange': '橙子是一种水果，橙色外皮，多汁果肉。',
            'broccoli': '西兰花是一种蔬菜，绿色花球，营养丰富。',
            'carrot': '胡萝卜是一种蔬菜，橙色根茎，富含维生素A。',
            'hot dog': '热狗是一种食品，通常由香肠和面包组成。',
            'pizza': '披萨是一种意大利食品，由面团、番茄酱和各种配料组成。',
            'donut': '甜甜圈是一种甜点，通常由油炸面团制成，表面有糖霜。',
            'cake': '蛋糕是一种甜点，通常由面粉、糖和鸡蛋制成。',
            'chair': '椅子是一种家具，用于坐人。',
            'couch': '沙发是一种家具，通常用于多人坐卧。',
            'potted plant': '盆栽植物是一种室内装饰，通常种植在花盆中。',
            'bed': '床是一种家具，用于睡觉和休息。',
            'dining table': '餐桌是一种家具，用于用餐。',
            'toilet': '马桶是一种卫生设备，用于排泄和冲洗。',
            'tv': '电视是一种电子设备，用于接收和播放电视节目。',
            'laptop': '笔记本电脑是一种便携式电子设备，用于工作和娱乐。',
            'mouse': '鼠标是一种计算机输入设备，用于控制光标。',
            'remote': '遥控器是一种电子设备，用于远程控制其他设备。',
            'keyboard': '键盘是一种计算机输入设备，用于输入文字和命令。',
            'cell phone': '手机是一种便携式通信设备，用于打电话和上网。',
            'microwave': '微波炉是一种厨房电器，用于加热和烹饪食物。',
            'oven': '烤箱是一种厨房电器，用于烘焙和烤制食物。',
            'toaster': '烤面包机是一种厨房电器，用于烤面包。',
            'sink': '水槽是一种厨房设备，用于洗涤餐具和食物。',
            'refrigerator': '冰箱是一种厨房电器，用于冷藏和冷冻食物。',
            'book': '书是一种出版物，由纸张和封面组成，用于存储和传播知识。',
            'clock': '时钟是一种计时设备，用于显示时间。',
            'vase': '花瓶是一种容器，通常用于插花。',
            'scissors': '剪刀是一种切割工具，由两个刀片组成。',
            'teddy bear': '泰迪熊是一种毛绒玩具，通常模仿熊的形象。',
            'hair drier': '吹风机是一种电器，用于吹干头发。',
            'toothbrush': '牙刷是一种清洁工具，用于刷牙。'
        }
    
    def get_knowledge(self, class_name: str) -> str:
        """获取指定类别的本体知识"""
        return self.knowledge.get(class_name, f"关于'{class_name}'的知识暂未录入。")

class ImageWidget(QWidget):
    """用于显示图像和检测结果的自定义控件"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.image = None
        self.detections = []
        self.title = ""
        self.font_scale = 0.5
        
        self.initUI()
    
    def initUI(self):
        self.layout = QVBoxLayout(self)
        
        self.title_label = QLabel(self.title)
        self.title_label.setAlignment(Qt.AlignCenter)
        self.title_label.setFont(QFont("SimHei", 12, QFont.Bold))
        self.layout.addWidget(self.title_label)
        
        self.image_label = QLabel()
        self.image_label.setAlignment(Qt.AlignCenter)
        self.image_label.setMinimumSize(100, 100)
        self.layout.addWidget(self.image_label)
        
        self.setLayout(self.layout)

    def setTitle(self, title: str):
        """设置标题"""
        self.title = title
        self.title_label.setText(title)

    def set_image(self, image: np.ndarray, detections: List[Dict], title: str, font_scale: float = 0.5):
        """设置要显示的图像和检测结果"""
        self.image = image.copy()
        self.detections = detections
        self.title = title
        self.font_scale = font_scale
        
        self.update_image()
    
    def update_image(self):
        """更新显示的图像，包括绘制边界框和标签"""
        if self.image is None:
            return
        
        # 复制图像，避免修改原图
        display_image = self.image.copy()
        
        # 绘制检测结果
        for det in self.detections:
            x1, y1, x2, y2 = map(int, det['bbox'])
            class_name = det['class_name']
            confidence = det['confidence']
            
            # 绘制边界框
            color = (0, 255, 0)  # 绿色
            cv2.rectangle(display_image, (x1, y1), (x2, y2), color, 2)
            
            # 绘制标签背景
            label = f"{class_name}: {confidence:.2f}"
            (label_width, label_height), _ = cv2.getTextSize(label, cv2.FONT_HERSHEY_SIMPLEX, self.font_scale, 2)
            cv2.rectangle(display_image, (x1, y1 - label_height - 10), (x1 + label_width, y1), color, -1)
            
            # 绘制标签文本
            cv2.putText(display_image, label, (x1, y1 - 5), cv2.FONT_HERSHEY_SIMPLEX, self.font_scale, (0, 0, 0), 2)
        
        # 更新标题
        self.title_label.setText(self.title)
        
        # 转换为Qt格式并显示
        height, width = display_image.shape[:2]
        if len(display_image.shape) == 3:
            channels = display_image.shape[2]
            bytesPerLine = channels * width
            qImg = QImage(display_image.data, width, height, bytesPerLine, QImage.Format_BGR888)
        else:
            bytesPerLine = width
            qImg = QImage(display_image.data, width, height, bytesPerLine, QImage.Format_Grayscale8)
        self.image_label.setPixmap(QPixmap.fromImage(qImg).scaled(
            self.image_label.width(), self.image_label.height(), 
            Qt.KeepAspectRatio, Qt.SmoothTransformation))
    
    def resizeEvent(self, event):
        """窗口大小改变时重新绘制图像"""
        if self.image is not None:
            self.update_image()
        super().resizeEvent(event)

class FeatureDisplayWidget(QWidget):
    """用于显示特征提取结果的自定义控件"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.feature_type = ""
        self.features = {}
        
        self.initUI()
    
    def initUI(self):
        self.layout = QVBoxLayout(self)
        
        self.title_label = QLabel(self.feature_type)
        self.title_label.setAlignment(Qt.AlignCenter)
        self.title_label.setFont(QFont("SimHei", 12, QFont.Bold))
        self.layout.addWidget(self.title_label)
        
        self.feature_text = QTextEdit()
        self.feature_text.setReadOnly(True)
        self.feature_text.setFont(QFont("SimHei", 10))
        self.layout.addWidget(self.feature_text)
        
        self.setLayout(self.layout)
    
    def set_features(self, feature_type: str, features: Dict):
        """设置要显示的特征类型和特征值"""
        self.feature_type = feature_type
        self.features = features
        
        self.update_display()
    
    def update_display(self):
        """更新显示的特征信息"""
        self.title_label.setText(f"{self.feature_type}特征")
        
        # 格式化特征信息
        feature_text = ""
        for key, value in self.features.items():
            if key != 'feature_vector':  # 不显示特征向量，因为它是内部使用的
                feature_text += f"{key}: {value:.4f}\n"
        
        self.feature_text.setText(feature_text)

class MultiModalRecognitionApp(QMainWindow):
    """多模态图像识别应用主窗口"""
    
    def __init__(self):
        super().__init__()
        print("正在初始化多模态识别应用...")

        self.visible_image = None
        self.thermal_image = None
        self.sar_image = None

        self.visible_features = None
        self.thermal_features = None
        self.sar_features = None

        self.scene_result = ""
        self.scene_explanation = ""

        print("正在初始化目标检测器...")
        self.object_detector = ObjectDetector()
        print("正在初始化知识库...")
        self.knowledge_base = KnowledgeBase()

        self.error_level = 50  # 默认误差级别

        print("正在初始化用户界面...")
        self.initUI()
        print("应用程序初始化完成")
    
    def initUI(self):
        """初始化用户界面"""
        self.setWindowTitle("多模态图像识别系统")
        self.setGeometry(100, 100, 1200, 800)
        
        # 创建中央部件和主布局
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        main_layout = QVBoxLayout(central_widget)
        
        # 创建标签页控件
        self.tabs = QTabWidget()
        
        # 创建图像输入标签页
        self.create_input_tab()
        
        # 创建特征提取标签页
        self.create_feature_tab()
        
        # 创建场景识别标签页
        self.create_scene_tab()
        
        # 创建目标检测标签页
        self.create_detection_tab()
        
        # 将标签页添加到标签页控件
        self.tabs.addTab(self.input_tab, "图像输入")
        self.tabs.addTab(self.feature_tab, "特征提取")
        self.tabs.addTab(self.scene_tab, "场景识别")
        self.tabs.addTab(self.detection_tab, "目标检测")
        
        # 将标签页控件添加到主布局
        main_layout.addWidget(self.tabs)
    
    def create_input_tab(self):
        """创建图像输入标签页"""
        self.input_tab = QWidget()
        input_layout = QVBoxLayout(self.input_tab)
        
        # 创建图像选择区域
        selection_layout = QHBoxLayout()
        
        # 可见光图像选择
        visible_group = QGroupBox("可见光图像")
        visible_layout = QVBoxLayout(visible_group)
        
        self.visible_path_label = QLabel("未选择图像")
        visible_layout.addWidget(self.visible_path_label)
        
        self.visible_select_button = QPushButton("选择图像")
        self.visible_select_button.clicked.connect(self.select_visible_image)
        visible_layout.addWidget(self.visible_select_button)
        
        selection_layout.addWidget(visible_group)
        
        # 红外图像选择
        thermal_group = QGroupBox("红外图像")
        thermal_layout = QVBoxLayout(thermal_group)
        
        self.thermal_path_label = QLabel("未选择图像")
        thermal_layout.addWidget(self.thermal_path_label)
        
        self.thermal_select_button = QPushButton("选择图像")
        self.thermal_select_button.clicked.connect(self.select_thermal_image)
        thermal_layout.addWidget(self.thermal_select_button)
        
        selection_layout.addWidget(thermal_group)
        
        # SAR图像选择
        sar_group = QGroupBox("SAR图像")
        sar_layout = QVBoxLayout(sar_group)
        
        self.sar_path_label = QLabel("未选择图像")
        sar_layout.addWidget(self.sar_path_label)
        
        self.sar_select_button = QPushButton("选择图像")
        self.sar_select_button.clicked.connect(self.select_sar_image)
        sar_layout.addWidget(self.sar_select_button)
        
        selection_layout.addWidget(sar_group)
        
        input_layout.addLayout(selection_layout)
        
        # 创建图像显示区域
        self.image_display_area = QWidget()
        image_display_layout = QHBoxLayout(self.image_display_area)
        
        self.visible_image_widget = ImageWidget()
        self.visible_image_widget.setTitle("可见光图像")
        image_display_layout.addWidget(self.visible_image_widget)
        
        self.thermal_image_widget = ImageWidget()
        self.thermal_image_widget.setTitle("红外图像")
        image_display_layout.addWidget(self.thermal_image_widget)
        
        self.sar_image_widget = ImageWidget()
        self.sar_image_widget.setTitle("SAR图像")
        image_display_layout.addWidget(self.sar_image_widget)
        
        input_layout.addWidget(self.image_display_area)
    
    def create_feature_tab(self):
        """创建特征提取标签页"""
        self.feature_tab = QWidget()
        feature_layout = QVBoxLayout(self.feature_tab)
        
        # 创建特征显示区域
        feature_display_layout = QHBoxLayout()
        
        self.visible_feature_widget = FeatureDisplayWidget()
        feature_display_layout.addWidget(self.visible_feature_widget)
        
        self.thermal_feature_widget = FeatureDisplayWidget()
        feature_display_layout.addWidget(self.thermal_feature_widget)
        
        self.sar_feature_widget = FeatureDisplayWidget()
        feature_display_layout.addWidget(self.sar_feature_widget)
        
        feature_layout.addLayout(feature_display_layout)
    
    def create_scene_tab(self):
        """创建场景识别标签页"""
        self.scene_tab = QWidget()
        scene_layout = QVBoxLayout(self.scene_tab)
        
        # 创建场景识别结果显示区域
        self.scene_result_label = QLabel("场景识别结果将显示在这里")
        self.scene_result_label.setAlignment(Qt.AlignCenter)
        self.scene_result_label.setFont(QFont("SimHei", 16, QFont.Bold))
        scene_layout.addWidget(self.scene_result_label)
        
        # 创建场景识别依据显示区域
        self.scene_explanation_text = QTextEdit()
        self.scene_explanation_text.setReadOnly(True)
        self.scene_explanation_text.setFont(QFont("SimHei", 10))
        scene_layout.addWidget(self.scene_explanation_text)
    
    def create_detection_tab(self):
        """创建目标检测标签页"""
        self.detection_tab = QWidget()
        detection_layout = QVBoxLayout(self.detection_tab)
        
        # 创建误差级别控制
        error_layout = QHBoxLayout()
        error_label = QLabel("识别误差级别:")
        error_layout.addWidget(error_label)
        
        self.error_slider = QSlider(Qt.Horizontal)
        self.error_slider.setMinimum(0)
        self.error_slider.setMaximum(100)
        self.error_slider.setValue(self.error_level)
        self.error_slider.valueChanged.connect(self.set_error_level)
        error_layout.addWidget(self.error_slider)
        
        self.error_value_label = QLabel(f"{self.error_level}")
        self.error_value_label.setFixedWidth(50)
        error_layout.addWidget(self.error_value_label)
        
        detection_layout.addLayout(error_layout)
        
        # 创建检测结果显示区域
        detection_display_layout = QHBoxLayout()
        
        self.before_correction_widget = ImageWidget()
        self.before_correction_widget.setTitle("识别策略引入前")
        detection_display_layout.addWidget(self.before_correction_widget)
        
        self.after_correction_widget = ImageWidget()
        self.after_correction_widget.setTitle("识别策略引入后")
        detection_display_layout.addWidget(self.after_correction_widget)
        
        detection_layout.addLayout(detection_display_layout)
        
        # 创建选中对象信息显示区域
        info_layout = QHBoxLayout()
        
        self.selected_object_widget = ImageWidget()
        self.selected_object_widget.setTitle("选中对象")
        info_layout.addWidget(self.selected_object_widget)
        
        self.object_info_text = QTextEdit()
        self.object_info_text.setReadOnly(True)
        self.object_info_text.setFont(QFont("SimHei", 10))
        info_layout.addWidget(self.object_info_text)
        
        detection_layout.addLayout(info_layout)
    
    def select_visible_image(self):
        """选择可见光图像"""
        options = QFileDialog.Options()
        file_name, _ = QFileDialog.getOpenFileName(
            self, "选择可见光图像", "", "图像文件 (*.png *.jpg *.jpeg);;视频文件 (*.mp4 *.avi)", options=options)
        if file_name:
            self.visible_path_label.setText(file_name)
            self.visible_image = cv2.imread(file_name)
            self.visible_image_widget.set_image(self.visible_image, [], "可见光图像")
            self.process_data()
    
    def select_thermal_image(self):
        """选择红外图像"""
        options = QFileDialog.Options()
        file_name, _ = QFileDialog.getOpenFileName(
            self, "选择红外图像", "", "图像文件 (*.png *.jpg *.jpeg);;视频文件 (*.mp4 *.avi)", options=options)
        if file_name:
            self.thermal_path_label.setText(file_name)
            self.thermal_image = cv2.imread(file_name)
            self.thermal_image_widget.set_image(self.thermal_image, [], "红外图像")
            self.process_data()
    
    def select_sar_image(self):
        """选择SAR图像"""
        options = QFileDialog.Options()
        file_name, _ = QFileDialog.getOpenFileName(
            self, "选择SAR图像", "", "图像文件 (*.png *.jpg *.jpeg);;视频文件 (*.mp4 *.avi)", options=options)
        if file_name:
            self.sar_path_label.setText(file_name)
            self.sar_image = cv2.imread(file_name)
            self.sar_image_widget.set_image(self.sar_image, [], "SAR图像")
            self.process_data()
    
    def process_data(self):
        if self.visible_image is not None and self.thermal_image is not None and self.sar_image is not None:
            # 提取特征
            self.extract_features()
            
            # 场景识别
            self.recognize_scene()
            
            # 目标检测
            self.perform_detection()
    
    def extract_features(self):
        if self.visible_image is not None:
            self.visible_features = FeatureExtractor.extract_visible_features(self.visible_image)
            self.visible_feature_widget.set_features("可见光", self.visible_features)
        
        if self.thermal_image is not None:
            self.thermal_features = FeatureExtractor.extract_thermal_features(self.thermal_image)
            self.thermal_feature_widget.set_features("红外", self.thermal_features)
        
        if self.sar_image is not None:
            self.sar_features = FeatureExtractor.extract_sar_features(self.sar_image)
            self.sar_feature_widget.set_features("SAR", self.sar_features)
    
    def recognize_scene(self):
        if self.visible_features is not None and self.thermal_features is not None and self.sar_features is not None:
            self.scene_result, self.scene_explanation = SceneClassifier.classify_scene(
                self.visible_features, self.thermal_features, self.sar_features)
            self.scene_result_label.setText(f"场景识别结果: {self.scene_result}")
            self.scene_explanation_text.setText(self.scene_explanation)
    
    def perform_detection(self):
        if self.visible_image is not None:
            self.detection_worker = DetectionWorker(self.object_detector, self.visible_image, self.error_level)
            self.detection_worker.detection_complete.connect(self.update_detection_results)
            self.detection_worker.start()
    
    def update_detection_results(self, detections_with_error, corrected_detections, image):
        self.before_correction_widget.set_image(image, detections_with_error, "识别策略引入前")
        self.after_correction_widget.set_image(image, corrected_detections, "识别策略引入后")
        
        # 保存过程数据
        self.save_process_data(detections_with_error, corrected_detections)
    
    def save_process_data(self, detections_with_error, corrected_detections):
        import json
        data = {
            'visible_features': self.visible_features,
            'thermal_features': self.thermal_features,
            'sar_features': self.sar_features,
            'scene_result': self.scene_result,
            'scene_explanation': self.scene_explanation,
            'detections_with_error': detections_with_error,
            'corrected_detections': corrected_detections
        }
        with open('process_data.json', 'w') as f:
            json.dump(data, f, indent=4)
    
    def set_error_level(self, value):
        self.error_level = value
        self.error_value_label.setText(f"{value}")

if __name__ == '__main__':
    app = QApplication(sys.argv)
    window = MultiModalRecognitionApp()
    window.show()
    sys.exit(app.exec_())