#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
多模态识别系统使用示例
演示如何使用系统的各个组件
"""

import os
import numpy as np
import cv2
from multimodal_recognition_system import (
    DataTypeDetector,
    MultiDomainFeatureExtractor,
    SceneRecognizer,
    ProcessDataManager,
    DetectionSystemManager,
    YOLODetectionProcessor,
    GradientFeatureExtractor
)

def example_data_type_detection():
    """示例：数据类型检测"""
    print("=" * 50)
    print("数据类型检测示例")
    print("=" * 50)
    
    # 检测现有图像
    test_files = [
        "data/image/image_2_450.jpg",
        "visible/000000.jpg",
        "test_images/visible_day.jpg"
    ]
    
    for file_path in test_files:
        if os.path.exists(file_path):
            result = DataTypeDetector.detect_data_type(file_path)
            print(f"文件: {file_path}")
            print(f"  数据类型: {result['data_type']}")
            print(f"  探测体制: {result['detection_systems']}")
            print()

def example_feature_extraction():
    """示例：特征提取"""
    print("=" * 50)
    print("多域特征提取示例")
    print("=" * 50)
    
    # 使用现有图像或创建测试图像
    image_path = "visible/000000.jpg"
    if not os.path.exists(image_path):
        # 创建测试图像
        test_img = np.random.randint(0, 255, (480, 640, 3), dtype=np.uint8)
        cv2.rectangle(test_img, (100, 100), (300, 300), (255, 255, 255), -1)
        cv2.imwrite("test_feature_image.jpg", test_img)
        image_path = "test_feature_image.jpg"
    
    # 加载图像
    image = cv2.imread(image_path)
    if image is None:
        print(f"无法加载图像: {image_path}")
        return
    
    print(f"处理图像: {image_path}")
    print(f"图像尺寸: {image.shape}")
    
    # 提取空域特征
    spatial_features = MultiDomainFeatureExtractor.extract_spatial_features(image)
    print("\n空域特征:")
    for key, value in spatial_features.items():
        print(f"  {key}: {value:.4f}")
    
    # 提取频域特征
    frequency_features = MultiDomainFeatureExtractor.extract_frequency_features(image)
    print("\n频域特征:")
    for key, value in frequency_features.items():
        if isinstance(value, (int, float)):
            print(f"  {key}: {value:.4f}")
    
    # 提取小波域特征
    wavelet_features = MultiDomainFeatureExtractor.extract_wavelet_features(image)
    print("\n小波域特征:")
    for key, value in wavelet_features.items():
        if isinstance(value, (int, float)):
            print(f"  {key}: {value:.4f}")

def example_scene_recognition():
    """示例：场景识别"""
    print("=" * 50)
    print("场景识别示例")
    print("=" * 50)
    
    # 创建模拟的白天和夜晚图像
    day_img = np.random.randint(150, 255, (200, 200, 3), dtype=np.uint8)
    night_img = np.random.randint(0, 80, (200, 200, 3), dtype=np.uint8)
    
    # 提取特征
    day_features = MultiDomainFeatureExtractor.extract_spatial_features(day_img)
    night_features = MultiDomainFeatureExtractor.extract_spatial_features(night_img)
    
    print("白天图像特征:")
    print(f"  平均亮度: {day_features['mean_intensity']:.2f}")
    print(f"  对比度: {day_features['contrast']:.2f}")
    
    print("\n夜晚图像特征:")
    print(f"  平均亮度: {night_features['mean_intensity']:.2f}")
    print(f"  对比度: {night_features['contrast']:.2f}")
    
    # 场景识别
    scene_result = SceneRecognizer.recognize_scene(day_features, night_features)
    print(f"\n场景识别结果:")
    print(f"  场景: {scene_result['scene']}")
    print(f"  置信度: {scene_result['confidence']:.2f}")
    print(f"  判断依据: {scene_result['judgment_basis']}")

def example_detection_system_management():
    """示例：探测体制管理"""
    print("=" * 50)
    print("探测体制管理示例")
    print("=" * 50)
    
    manager = DetectionSystemManager()
    
    # 测试不同场景下的策略
    detection_systems = ['visible_light', 'thermal_infrared']
    
    for scene in ['day', 'night', 'unknown']:
        strategy = manager.get_detection_strategy(detection_systems, scene)
        print(f"\n{scene.upper()}场景:")
        print(f"  策略: {strategy['strategy']}")
        print(f"  探测体制: {strategy['detection_systems']}")
        print(f"  权重分配:")
        for system, weight in strategy['weights'].items():
            print(f"    {system}: {weight:.3f}")

def example_gradient_features():
    """示例：梯度特征提取"""
    print("=" * 50)
    print("梯度特征提取示例")
    print("=" * 50)
    
    # 创建测试图像
    test_img = np.random.randint(0, 255, (200, 200, 3), dtype=np.uint8)
    # 添加一些结构
    cv2.rectangle(test_img, (50, 50), (150, 150), (255, 255, 255), -1)
    cv2.circle(test_img, (100, 100), 30, (0, 0, 0), -1)
    
    # 定义测试区域
    bbox = [40, 40, 160, 160]  # x1, y1, x2, y2
    
    # 提取梯度特征
    gradient_features = GradientFeatureExtractor.extract_gradient_features(test_img, bbox)
    
    print(f"区域: {bbox}")
    print(f"梯度统计:")
    print(f"  平均梯度: {gradient_features['grad_mean']:.2f}")
    print(f"  梯度标准差: {gradient_features['grad_std']:.2f}")
    print(f"  最大梯度: {gradient_features['grad_max']:.2f}")
    print(f"  方向直方图: {gradient_features['direction_histogram']}")

def example_process_data_management():
    """示例：过程数据管理"""
    print("=" * 50)
    print("过程数据管理示例")
    print("=" * 50)
    
    # 创建数据管理器
    data_manager = ProcessDataManager()
    
    print(f"会话目录: {data_manager.get_session_dir()}")
    
    # 记录处理步骤
    data_manager.log_process_step("测试步骤1", {"test": "data1", "value": 123})
    data_manager.log_process_step("测试步骤2", {"test": "data2", "value": 456})
    
    # 保存测试图像
    test_img = np.random.randint(0, 255, (100, 100, 3), dtype=np.uint8)
    image_path = data_manager.save_image(test_img, "test_image")
    
    print(f"图像已保存: {image_path}")
    print("处理日志已自动保存")

def run_all_examples():
    """运行所有示例"""
    print("多模态识别系统使用示例")
    print("=" * 60)
    
    examples = [
        example_data_type_detection,
        example_feature_extraction,
        example_scene_recognition,
        example_detection_system_management,
        example_gradient_features,
        example_process_data_management
    ]
    
    for i, example_func in enumerate(examples, 1):
        try:
            print(f"\n示例 {i}:")
            example_func()
        except Exception as e:
            print(f"示例 {i} 执行失败: {e}")
        
        if i < len(examples):
            input("\n按回车键继续下一个示例...")
    
    print("\n" + "=" * 60)
    print("所有示例演示完成！")
    print("要启动完整的GUI系统，请运行: python run_multimodal_system.py")

if __name__ == "__main__":
    run_all_examples()
