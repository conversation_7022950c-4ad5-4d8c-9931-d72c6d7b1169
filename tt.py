import sys
import os
import random
import numpy as np
import cv2
import json
import datetime
import threading
import time
from scipy import fftpack
import pywt
from PyQt5.QtWidgets import (QApplication, QMainWindow, QTabWidget, QWidget, QVBoxLayout,
                            QHBoxLayout, QLabel, QPushButton, QFileDialog, QSlider,
                            QGroupBox, QGridLayout, QTextEdit, QComboBox, QSplitter,
                            QProgressBar, QScrollArea, QFrame, QSplitter)
from PyQt5.QtGui import QPixmap, QImage, QFont, QPainter, QPen, QColor
from PyQt5.QtCore import Qt, QThread, pyqtSignal, QTimer, QMutex, QMutexLocker
from ultralytics import YOLO
from typing import List, Dict, Tuple, Optional
import matplotlib.pyplot as plt
from matplotlib.backends.backend_qt5agg import FigureCanvasQTAgg as FigureCanvas
from matplotlib.figure import Figure

# 确保中文显示正常
# QFontDatabase.addApplicationFont("simhei.ttf")  # 如果系统中没有SimHei字体，需要提供字体文件

class MultiDomainFeatureExtractor:
    """多域特征提取器，用于从不同类型的图像中提取空域、频域、小波域特征"""

    @staticmethod
    def extract_spatial_features(image: np.ndarray) -> Dict:
        """提取空域特征"""
        if len(image.shape) == 3:
            gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
        else:
            gray = image

        # 基本统计特征
        mean_intensity = np.mean(gray)
        std_intensity = np.std(gray)
        variance = np.var(gray)
        skewness = np.mean(((gray - mean_intensity) / std_intensity) ** 3)
        kurtosis = np.mean(((gray - mean_intensity) / std_intensity) ** 4)

        # 边缘特征
        edges = cv2.Canny(gray, 50, 150)
        edge_density = np.count_nonzero(edges) / (edges.shape[0] * edges.shape[1])

        # 纹理特征（基于灰度共生矩阵的简化版本）
        # 计算水平方向的灰度差分
        diff_h = np.abs(gray[:, 1:].astype(np.float32) - gray[:, :-1].astype(np.float32))
        diff_v = np.abs(gray[1:, :].astype(np.float32) - gray[:-1, :].astype(np.float32))

        contrast = np.mean(diff_h) + np.mean(diff_v)
        homogeneity = 1.0 / (1.0 + contrast)
        energy = np.sum(gray.astype(np.float32) ** 2) / (gray.shape[0] * gray.shape[1])

        return {
            'mean_intensity': mean_intensity,
            'std_intensity': std_intensity,
            'variance': variance,
            'skewness': skewness,
            'kurtosis': kurtosis,
            'edge_density': edge_density,
            'contrast': contrast,
            'homogeneity': homogeneity,
            'energy': energy
        }

    @staticmethod
    def extract_frequency_features(image: np.ndarray) -> Dict:
        """提取频域特征"""
        if len(image.shape) == 3:
            gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
        else:
            gray = image

        # 二维傅里叶变换
        f_transform = fftpack.fft2(gray)
        f_shift = fftpack.fftshift(f_transform)
        magnitude_spectrum = np.abs(f_shift)

        # 频域统计特征
        mean_magnitude = np.mean(magnitude_spectrum)
        std_magnitude = np.std(magnitude_spectrum)
        max_magnitude = np.max(magnitude_spectrum)

        # 频域能量分布
        total_energy = np.sum(magnitude_spectrum ** 2)

        # 计算低频、中频、高频能量比例
        h, w = magnitude_spectrum.shape
        center_h, center_w = h // 2, w // 2

        # 低频区域（中心1/4区域）
        low_freq_region = magnitude_spectrum[center_h-h//8:center_h+h//8, center_w-w//8:center_w+w//8]
        low_freq_energy = np.sum(low_freq_region ** 2) / total_energy

        # 高频区域（边缘区域）
        high_freq_mask = np.ones_like(magnitude_spectrum)
        high_freq_mask[center_h-h//4:center_h+h//4, center_w-w//4:center_w+w//4] = 0
        high_freq_energy = np.sum((magnitude_spectrum * high_freq_mask) ** 2) / total_energy

        # 中频能量
        mid_freq_energy = 1.0 - low_freq_energy - high_freq_energy

        return {
            'mean_magnitude': mean_magnitude,
            'std_magnitude': std_magnitude,
            'max_magnitude': max_magnitude,
            'total_energy': total_energy,
            'low_freq_energy_ratio': low_freq_energy,
            'mid_freq_energy_ratio': mid_freq_energy,
            'high_freq_energy_ratio': high_freq_energy,
            'magnitude_spectrum': magnitude_spectrum  # 用于可视化
        }

    @staticmethod
    def extract_wavelet_features(image: np.ndarray) -> Dict:
        """提取小波域特征"""
        if len(image.shape) == 3:
            gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
        else:
            gray = image

        # 小波变换（使用Daubechies小波）
        coeffs = pywt.dwt2(gray, 'db4')
        cA, (cH, cV, cD) = coeffs

        # 各子带的统计特征
        # 低频子带（近似系数）
        ca_mean = np.mean(cA)
        ca_std = np.std(cA)
        ca_energy = np.sum(cA ** 2)

        # 水平细节系数
        ch_mean = np.mean(cH)
        ch_std = np.std(cH)
        ch_energy = np.sum(cH ** 2)

        # 垂直细节系数
        cv_mean = np.mean(cV)
        cv_std = np.std(cV)
        cv_energy = np.sum(cV ** 2)

        # 对角细节系数
        cd_mean = np.mean(cD)
        cd_std = np.std(cD)
        cd_energy = np.sum(cD ** 2)

        # 总能量
        total_energy = ca_energy + ch_energy + cv_energy + cd_energy

        # 能量比例
        ca_energy_ratio = ca_energy / total_energy
        ch_energy_ratio = ch_energy / total_energy
        cv_energy_ratio = cv_energy / total_energy
        cd_energy_ratio = cd_energy / total_energy

        return {
            'ca_mean': ca_mean,
            'ca_std': ca_std,
            'ca_energy_ratio': ca_energy_ratio,
            'ch_mean': ch_mean,
            'ch_std': ch_std,
            'ch_energy_ratio': ch_energy_ratio,
            'cv_mean': cv_mean,
            'cv_std': cv_std,
            'cv_energy_ratio': cv_energy_ratio,
            'cd_mean': cd_mean,
            'cd_std': cd_std,
            'cd_energy_ratio': cd_energy_ratio,
            'wavelet_coeffs': coeffs  # 用于可视化
        }

    @staticmethod
    def extract_visible_features(image: np.ndarray) -> Dict:
        """从可见光图像中提取多域特征"""
        spatial_features = MultiDomainFeatureExtractor.extract_spatial_features(image)
        frequency_features = MultiDomainFeatureExtractor.extract_frequency_features(image)
        wavelet_features = MultiDomainFeatureExtractor.extract_wavelet_features(image)

        # 颜色特征（仅适用于可见光图像）
        if len(image.shape) == 3:
            hsv = cv2.cvtColor(image, cv2.COLOR_BGR2HSV)
            mean_hue = np.mean(hsv[:, :, 0])
            mean_saturation = np.mean(hsv[:, :, 1])
            mean_value = np.mean(hsv[:, :, 2])

            # RGB通道统计
            b_mean, g_mean, r_mean = np.mean(image, axis=(0, 1))
            b_std, g_std, r_std = np.std(image, axis=(0, 1))

            color_features = {
                'mean_hue': mean_hue,
                'mean_saturation': mean_saturation,
                'mean_value': mean_value,
                'r_mean': r_mean,
                'g_mean': g_mean,
                'b_mean': b_mean,
                'r_std': r_std,
                'g_std': g_std,
                'b_std': b_std
            }
        else:
            color_features = {}

        return {
            'spatial': spatial_features,
            'frequency': frequency_features,
            'wavelet': wavelet_features,
            'color': color_features,
            'image_type': 'visible'
        }

    @staticmethod
    def extract_thermal_features(image: np.ndarray) -> Dict:
        """从红外图像中提取多域特征"""
        spatial_features = MultiDomainFeatureExtractor.extract_spatial_features(image)
        frequency_features = MultiDomainFeatureExtractor.extract_frequency_features(image)
        wavelet_features = MultiDomainFeatureExtractor.extract_wavelet_features(image)

        # 红外特有特征
        if len(image.shape) == 3:
            gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
        else:
            gray = image

        # 温度相关特征（假设像素值代表相对温度）
        mean_temp = np.mean(gray)
        max_temp = np.max(gray)
        min_temp = np.min(gray)
        temp_range = max_temp - min_temp
        temp_variance = np.var(gray)

        # 热点检测
        threshold = mean_temp + np.std(gray)
        _, binary = cv2.threshold(gray, threshold, 255, cv2.THRESH_BINARY)
        hotspots_ratio = np.count_nonzero(binary) / (binary.shape[0] * binary.shape[1])

        # 温度梯度特征
        grad_x = cv2.Sobel(gray, cv2.CV_64F, 1, 0, ksize=3)
        grad_y = cv2.Sobel(gray, cv2.CV_64F, 0, 1, ksize=3)
        gradient_magnitude = np.sqrt(grad_x**2 + grad_y**2)
        mean_gradient = np.mean(gradient_magnitude)

        thermal_features = {
            'mean_temp': mean_temp,
            'max_temp': max_temp,
            'min_temp': min_temp,
            'temp_range': temp_range,
            'temp_variance': temp_variance,
            'hotspots_ratio': hotspots_ratio,
            'mean_gradient': mean_gradient
        }

        return {
            'spatial': spatial_features,
            'frequency': frequency_features,
            'wavelet': wavelet_features,
            'thermal': thermal_features,
            'image_type': 'thermal'
        }

    @staticmethod
    def extract_sar_features(image: np.ndarray) -> Dict:
        """从SAR图像中提取多域特征"""
        spatial_features = MultiDomainFeatureExtractor.extract_spatial_features(image)
        frequency_features = MultiDomainFeatureExtractor.extract_frequency_features(image)
        wavelet_features = MultiDomainFeatureExtractor.extract_wavelet_features(image)

        # SAR特有特征
        if len(image.shape) == 3:
            gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
        else:
            gray = image

        # 后向散射特征
        mean_backscatter = np.mean(gray)
        std_backscatter = np.std(gray)

        # 纹理特征（使用梯度计算）
        gradient_x = cv2.Sobel(gray, cv2.CV_64F, 1, 0, ksize=3)
        gradient_y = cv2.Sobel(gray, cv2.CV_64F, 0, 1, ksize=3)
        gradient_magnitude = np.sqrt(gradient_x**2 + gradient_y**2)

        contrast = np.std(gradient_magnitude)
        homogeneity = 1.0 / (1.0 + np.var(gray))
        energy = np.sum(gray**2) / (gray.shape[0] * gray.shape[1])
        correlation = np.corrcoef(gradient_x.flatten(), gradient_y.flatten())[0,1]
        if np.isnan(correlation):
            correlation = 0.0

        sar_features = {
            'mean_backscatter': mean_backscatter,
            'std_backscatter': std_backscatter,
            'contrast': contrast,
            'homogeneity': homogeneity,
            'energy': energy,
            'correlation': correlation
        }

        return {
            'spatial': spatial_features,
            'frequency': frequency_features,
            'wavelet': wavelet_features,
            'sar': sar_features,
            'image_type': 'sar'
        }
    
    @staticmethod
    def extract_thermal_features(image: np.ndarray) -> Dict:
        """从红外图像中提取特征"""
        # 假设输入的红外图像是单通道的，如果是三通道则转换为单通道
        if len(image.shape) == 3:
            gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
        else:
            gray = image
        
        # 计算温度统计（这里用像素值表示相对温度）
        mean_temp = np.mean(gray)
        max_temp = np.max(gray)
        min_temp = np.min(gray)
        temp_variance = np.var(gray)
        
        # 热点检测
        _, binary = cv2.threshold(gray, mean_temp + np.std(gray), 255, cv2.THRESH_BINARY)
        hotspots_ratio = np.count_nonzero(binary) / (binary.shape[0] * binary.shape[1])
        
        return {
            'mean_temp': mean_temp,
            'max_temp': max_temp,
            'min_temp': min_temp,
            'temp_variance': temp_variance,
            'hotspots_ratio': hotspots_ratio,
            'feature_vector': [mean_temp, max_temp, min_temp, temp_variance, hotspots_ratio]
        }
    
    @staticmethod
    def extract_sar_features(image: np.ndarray) -> Dict:
        """从SAR图像中提取特征"""
        # 假设输入的SAR图像是单通道的
        if len(image.shape) == 3:
            gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
        else:
            gray = image
        
        # 计算后向散射统计
        mean_backscatter = np.mean(gray)
        std_backscatter = np.std(gray)
        
        # 纹理特征（简化版本，替代GLCM）
        # 使用梯度计算纹理特征
        gradient_x = cv2.Sobel(gray, cv2.CV_64F, 1, 0, ksize=3)
        gradient_y = cv2.Sobel(gray, cv2.CV_64F, 0, 1, ksize=3)
        gradient_magnitude = np.sqrt(gradient_x**2 + gradient_y**2)

        contrast = np.std(gradient_magnitude)
        homogeneity = 1.0 / (1.0 + np.var(gray))
        energy = np.sum(gray**2) / (gray.shape[0] * gray.shape[1])
        correlation = np.corrcoef(gradient_x.flatten(), gradient_y.flatten())[0,1]
        if np.isnan(correlation):
            correlation = 0.0
        
        return {
            'mean_backscatter': mean_backscatter,
            'std_backscatter': std_backscatter,
            'contrast': contrast,
            'homogeneity': homogeneity,
            'energy': energy,
            'correlation': correlation,
            'feature_vector': [mean_backscatter, std_backscatter, contrast, homogeneity, energy, correlation]
        }

class EnhancedSceneClassifier:
    """增强的场景分类器，基于可见光和红外特征差异判断白天/黑夜"""

    @staticmethod
    def classify_scene(visible_features: Dict, thermal_features: Dict, sar_features: Dict) -> Tuple[str, str]:
        """
        基于多模态特征差异分类场景为白天或黑夜

        返回:
            分类结果 (str): "白天" 或 "黑夜"
            判别依据 (str): 详细的特征差异分析和判断依据
        """
        # 提取关键特征
        visible_spatial = visible_features['spatial']
        visible_color = visible_features['color']
        thermal_spatial = thermal_features['spatial']
        thermal_thermal = thermal_features['thermal']

        # 可见光特征分析
        visible_brightness = visible_spatial['mean_intensity']
        visible_contrast = visible_spatial['contrast']
        visible_saturation = visible_color.get('mean_saturation', 0)
        visible_edge_density = visible_spatial['edge_density']

        # 红外特征分析
        thermal_intensity = thermal_spatial['mean_intensity']
        thermal_variance = thermal_thermal['temp_variance']
        thermal_hotspots = thermal_thermal['hotspots_ratio']
        thermal_gradient = thermal_thermal['mean_gradient']

        # 特征差异计算
        intensity_diff = abs(visible_brightness - thermal_intensity)
        contrast_diff = abs(visible_spatial['contrast'] - thermal_spatial['contrast'])

        # 白天/黑夜判断逻辑
        day_indicators = []
        night_indicators = []

        # 可见光亮度判断
        if visible_brightness > 120:
            day_indicators.append(f"可见光平均亮度为{visible_brightness:.1f}，明显偏高")
        elif visible_brightness < 60:
            night_indicators.append(f"可见光平均亮度为{visible_brightness:.1f}，明显偏低")

        # 可见光饱和度判断
        if visible_saturation > 80:
            day_indicators.append(f"可见光饱和度为{visible_saturation:.1f}，色彩丰富")
        elif visible_saturation < 30:
            night_indicators.append(f"可见光饱和度为{visible_saturation:.1f}，色彩单调")

        # 红外温度特征判断
        if thermal_variance > 500:
            day_indicators.append(f"红外温度方差为{thermal_variance:.1f}，温度分布差异大")
        elif thermal_variance < 100:
            night_indicators.append(f"红外温度方差为{thermal_variance:.1f}，温度分布均匀")

        # 热点比例判断
        if thermal_hotspots > 0.1:
            day_indicators.append(f"红外热点比例为{thermal_hotspots:.3f}，存在明显热源")
        elif thermal_hotspots < 0.02:
            night_indicators.append(f"红外热点比例为{thermal_hotspots:.3f}，热源稀少")

        # 可见光与红外强度差异判断
        if intensity_diff > 50:
            if visible_brightness > thermal_intensity:
                day_indicators.append(f"可见光亮度({visible_brightness:.1f})明显高于红外强度({thermal_intensity:.1f})，差值{intensity_diff:.1f}")
            else:
                night_indicators.append(f"红外强度({thermal_intensity:.1f})明显高于可见光亮度({visible_brightness:.1f})，差值{intensity_diff:.1f}")

        # 边缘密度判断
        if visible_edge_density > 0.1:
            day_indicators.append(f"可见光边缘密度为{visible_edge_density:.3f}，细节丰富")
        elif visible_edge_density < 0.05:
            night_indicators.append(f"可见光边缘密度为{visible_edge_density:.3f}，细节模糊")

        # 综合判断
        day_score = len(day_indicators)
        night_score = len(night_indicators)

        if day_score > night_score:
            result = "白天"
            primary_reason = "白天判断依据"
            reasons = day_indicators
        elif night_score > day_score:
            result = "黑夜"
            primary_reason = "黑夜判断依据"
            reasons = night_indicators
        else:
            # 平分时使用亮度作为主要判断依据
            if visible_brightness > 90:
                result = "白天"
                primary_reason = "亮度主导的白天判断"
                reasons = [f"可见光亮度{visible_brightness:.1f}超过阈值90"]
            else:
                result = "黑夜"
                primary_reason = "亮度主导的黑夜判断"
                reasons = [f"可见光亮度{visible_brightness:.1f}低于阈值90"]

        # 构建详细的判断依据
        explanation = f"场景识别结果: {result}\n\n"
        explanation += f"{primary_reason}:\n"
        for i, reason in enumerate(reasons, 1):
            explanation += f"{i}. {reason}\n"

        explanation += f"\n关键特征对比:\n"
        explanation += f"• 可见光-红外亮度差异: {intensity_diff:.1f}\n"
        explanation += f"• 可见光-红外对比度差异: {contrast_diff:.1f}\n"
        explanation += f"• 红外温度梯度: {thermal_gradient:.1f}\n"

        explanation += f"\n因为可见光、红外的"
        if len(reasons) > 0:
            explanation += "、".join([reason.split("，")[0] for reason in reasons[:2]])
        explanation += f"等特征存在明显差异，所以判断为{result}"

        return result, explanation

class DetectionRegimeSelector:
    """探测体制选择器，根据输入数据类型和场景自动选择识别策略"""

    @staticmethod
    def determine_detection_regime(available_modalities: List[str], scene_result: str) -> Dict:
        """
        根据可用模态和场景结果确定探测体制

        Args:
            available_modalities: 可用的模态列表 ['visible', 'thermal', 'sar']
            scene_result: 场景识别结果 ('白天' 或 '黑夜')

        Returns:
            包含探测体制信息的字典
        """
        num_modalities = len(available_modalities)

        if num_modalities == 1:
            regime_type = "单探测体制识别"
            strategy = "single_modality"
        elif num_modalities == 2:
            regime_type = "特征融合识别策略"
            strategy = "feature_fusion"
        else:
            regime_type = "决策融合识别策略"
            strategy = "decision_fusion"

        # 根据场景和模态分配权重
        weights = DetectionRegimeSelector._calculate_modality_weights(
            available_modalities, scene_result)

        # 选择主导模态
        primary_modality = max(weights.keys(), key=lambda k: weights[k])

        return {
            'regime_type': regime_type,
            'strategy': strategy,
            'available_modalities': available_modalities,
            'weights': weights,
            'primary_modality': primary_modality,
            'scene_context': scene_result,
            'description': DetectionRegimeSelector._generate_regime_description(
                regime_type, available_modalities, weights, scene_result)
        }

    @staticmethod
    def _calculate_modality_weights(modalities: List[str], scene: str) -> Dict[str, float]:
        """根据场景计算各模态的权重"""
        weights = {}

        if scene == "白天":
            # 白天场景权重分配
            if 'visible' in modalities:
                weights['visible'] = 0.6  # 可见光在白天效果最好
            if 'thermal' in modalities:
                weights['thermal'] = 0.25  # 红外作为辅助
            if 'sar' in modalities:
                weights['sar'] = 0.15  # SAR权重最低
        else:  # 黑夜
            # 黑夜场景权重分配
            if 'visible' in modalities:
                weights['visible'] = 0.2  # 可见光在黑夜效果差
            if 'thermal' in modalities:
                weights['thermal'] = 0.6  # 红外在黑夜效果最好
            if 'sar' in modalities:
                weights['sar'] = 0.2  # SAR不受光照影响

        # 归一化权重
        total_weight = sum(weights.values())
        if total_weight > 0:
            weights = {k: v/total_weight for k, v in weights.items()}

        return weights

    @staticmethod
    def _generate_regime_description(regime_type: str, modalities: List[str],
                                   weights: Dict[str, float], scene: str) -> str:
        """生成探测体制描述"""
        description = f"当前探测体制: {regime_type}\n\n"
        description += f"可用模态: {', '.join(modalities)}\n"
        description += f"场景环境: {scene}\n\n"

        description += "权重分配:\n"
        for modality, weight in weights.items():
            modality_name = {'visible': '可见光', 'thermal': '红外', 'sar': 'SAR'}[modality]
            description += f"• {modality_name}: {weight:.2f}\n"

        # 添加策略说明
        if regime_type == "单探测体制识别":
            description += f"\n策略说明: 仅使用{list(weights.keys())[0]}模态进行目标识别"
        elif regime_type == "特征融合识别策略":
            description += "\n策略说明: 在特征层面融合多模态信息，提高识别精度"
        else:
            description += "\n策略说明: 各模态独立识别后在决策层面融合，提高系统鲁棒性"

        # 添加场景适应性说明
        if scene == "白天":
            description += "\n场景适应: 白天光照充足，可见光权重较高"
        else:
            description += "\n场景适应: 夜间光照不足，红外权重较高"

        return description

    @staticmethod
    def _calculate_visible_weight(features: Dict) -> float:
        """计算可见光图像的可识别权重"""
        # 亮度适中时权重高，过亮或过暗时权重低
        brightness_factor = 1.0 - abs(features['mean_brightness'] - 127.5) / 127.5
        # 边缘丰富时权重高
        edge_factor = min(features['edge_density'] * 5, 1.0)
        return (brightness_factor * 0.6 + edge_factor * 0.4) * 1.2  # 可见光权重放大
    
    @staticmethod
    def _calculate_thermal_weight(features: Dict) -> float:
        """计算红外图像的可识别权重"""
        # 温度方差大时权重高
        variance_factor = min(features['temp_variance'] / 10000.0, 1.0)
        # 热点多时权重高
        hotspot_factor = min(features['hotspots_ratio'] * 10, 1.0)
        return variance_factor * 0.6 + hotspot_factor * 0.4
    
    @staticmethod
    def _calculate_sar_weight(features: Dict) -> float:
        """计算SAR图像的可识别权重"""
        # 后向散射标准差大时权重高
        std_factor = min(features['std_backscatter'] / 50.0, 1.0)
        # 纹理对比度高时权重高
        contrast_factor = min(features['contrast'] / 1000.0, 1.0)
        return std_factor * 0.5 + contrast_factor * 0.5

class EnhancedObjectDetector:
    """增强的目标检测器，支持策略调整和多模态检测"""

    def __init__(self, model_path: str = "yolov8n.pt"):
        """初始化检测器"""
        try:
            print(f"正在加载YOLO模型: {model_path}")
            self.model = YOLO(model_path)
            self.class_names = self.model.names
            print("YOLO模型加载成功")
        except Exception as e:
            print(f"YOLO模型加载失败: {e}")
            self.model = None
            self.class_names = {}

    def detect_with_strategy_adjustment(self, image: np.ndarray, regime_info: Dict) -> Dict:
        """
        执行带策略调整的目标检测

        Args:
            image: 输入图像
            regime_info: 探测体制信息

        Returns:
            包含调整前后检测结果的字典
        """
        if self.model is None:
            print("YOLO模型未加载，返回空检测结果")
            return {
                'before_adjustment': [],
                'after_adjustment': [],
                'adjustment_info': "模型未加载"
            }

        # 原始检测
        original_results = self.model(image)
        original_detections = self._parse_yolo_results(original_results)

        # 应用策略调整（模拟识别误差）
        adjusted_detections_before = self._apply_strategy_degradation(
            original_detections, image.shape, regime_info)

        # 策略优化后的结果（接近原始结果）
        adjusted_detections_after = self._apply_strategy_enhancement(
            adjusted_detections_before, original_detections, regime_info)

        # 生成调整信息
        adjustment_info = self._generate_adjustment_info(
            original_detections, adjusted_detections_before,
            adjusted_detections_after, regime_info)

        return {
            'before_adjustment': adjusted_detections_before,
            'after_adjustment': adjusted_detections_after,
            'original': original_detections,
            'adjustment_info': adjustment_info
        }

    def _parse_yolo_results(self, results) -> List[Dict]:
        """解析YOLO检测结果"""
        detections = []

        if len(results) > 0 and results[0].boxes is not None:
            for box in results[0].boxes:
                class_id = int(box.cls)
                confidence = float(box.conf)
                x1, y1, x2, y2 = box.xyxy[0].tolist()

                detections.append({
                    'class_id': class_id,
                    'class_name': self.class_names.get(class_id, f'class_{class_id}'),
                    'confidence': confidence,
                    'bbox': [x1, y1, x2, y2],
                    'area': (x2 - x1) * (y2 - y1)
                })

        return detections

    def _apply_strategy_degradation(self, detections: List[Dict],
                                  image_shape: Tuple, regime_info: Dict) -> List[Dict]:
        """应用策略调整前的降级处理（模拟识别误差）"""
        degraded_detections = []

        # 根据探测体制确定降级程度
        primary_modality = regime_info.get('primary_modality', 'visible')
        scene = regime_info.get('scene_context', '白天')

        # 确定误差级别
        if scene == "黑夜" and primary_modality == 'visible':
            error_level = 30  # 可见光在夜间误差大
            confidence_reduction = 0.3
        elif scene == "白天" and primary_modality == 'thermal':
            error_level = 20  # 红外在白天误差中等
            confidence_reduction = 0.2
        else:
            error_level = 15  # 其他情况误差较小
            confidence_reduction = 0.15

        for det in detections:
            x1, y1, x2, y2 = det['bbox']

            # 添加位置误差
            x1_err = x1 + random.randint(-error_level, error_level)
            y1_err = y1 + random.randint(-error_level, error_level)
            x2_err = x2 + random.randint(-error_level, error_level)
            y2_err = y2 + random.randint(-error_level, error_level)

            # 确保边界框有效
            x1_err = max(0, min(x1_err, image_shape[1] - 1))
            y1_err = max(0, min(y1_err, image_shape[0] - 1))
            x2_err = max(x1_err + 1, min(x2_err, image_shape[1]))
            y2_err = max(y1_err + 1, min(y2_err, image_shape[0]))

            # 降低置信度
            degraded_confidence = max(0.1, det['confidence'] - confidence_reduction)

            degraded_detections.append({
                'class_id': det['class_id'],
                'class_name': det['class_name'],
                'confidence': degraded_confidence,
                'bbox': [x1_err, y1_err, x2_err, y2_err],
                'area': (x2_err - x1_err) * (y2_err - y1_err),
                'error_level': error_level,
                'confidence_reduction': confidence_reduction
            })

        return degraded_detections

    def _apply_strategy_enhancement(self, degraded_detections: List[Dict],
                                  original_detections: List[Dict],
                                  regime_info: Dict) -> List[Dict]:
        """应用策略优化后的增强处理"""
        enhanced_detections = []

        # 策略增强参数
        weights = regime_info.get('weights', {})
        primary_weight = max(weights.values()) if weights else 1.0

        for i, det in enumerate(degraded_detections):
            if i < len(original_detections):
                orig_det = original_detections[i]

                # 位置校正（向原始位置靠近）
                orig_bbox = orig_det['bbox']
                deg_bbox = det['bbox']

                # 加权平均校正
                correction_factor = primary_weight * 0.8  # 校正强度

                corrected_bbox = [
                    deg_bbox[0] + (orig_bbox[0] - deg_bbox[0]) * correction_factor,
                    deg_bbox[1] + (orig_bbox[1] - deg_bbox[1]) * correction_factor,
                    deg_bbox[2] + (orig_bbox[2] - deg_bbox[2]) * correction_factor,
                    deg_bbox[3] + (orig_bbox[3] - deg_bbox[3]) * correction_factor
                ]

                # 置信度提升
                confidence_boost = det.get('confidence_reduction', 0.15) * 0.9
                enhanced_confidence = min(1.0, det['confidence'] + confidence_boost)

                enhanced_detections.append({
                    'class_id': det['class_id'],
                    'class_name': det['class_name'],
                    'confidence': enhanced_confidence,
                    'bbox': corrected_bbox,
                    'area': (corrected_bbox[2] - corrected_bbox[0]) * (corrected_bbox[3] - corrected_bbox[1]),
                    'correction_factor': correction_factor,
                    'confidence_boost': confidence_boost
                })
            else:
                enhanced_detections.append(det)

        return enhanced_detections

    def _generate_adjustment_info(self, original: List[Dict], before: List[Dict],
                                after: List[Dict], regime_info: Dict) -> str:
        """生成策略调整信息"""
        info = f"策略调整分析报告\n\n"
        info += f"探测体制: {regime_info.get('regime_type', '未知')}\n"
        info += f"主导模态: {regime_info.get('primary_modality', '未知')}\n"
        info += f"场景环境: {regime_info.get('scene_context', '未知')}\n\n"

        info += f"检测目标数量:\n"
        info += f"• 原始检测: {len(original)}个\n"
        info += f"• 策略调整前: {len(before)}个\n"
        info += f"• 策略调整后: {len(after)}个\n\n"

        if before and after:
            # 计算平均置信度变化
            avg_conf_before = np.mean([det['confidence'] for det in before])
            avg_conf_after = np.mean([det['confidence'] for det in after])
            conf_improvement = avg_conf_after - avg_conf_before

            info += f"置信度变化:\n"
            info += f"• 调整前平均置信度: {avg_conf_before:.3f}\n"
            info += f"• 调整后平均置信度: {avg_conf_after:.3f}\n"
            info += f"• 置信度提升: {conf_improvement:.3f}\n\n"

            # 计算位置偏移
            if len(before) > 0 and len(after) > 0:
                bbox_shifts = []
                for i in range(min(len(before), len(after))):
                    before_center = [(before[i]['bbox'][0] + before[i]['bbox'][2])/2,
                                   (before[i]['bbox'][1] + before[i]['bbox'][3])/2]
                    after_center = [(after[i]['bbox'][0] + after[i]['bbox'][2])/2,
                                  (after[i]['bbox'][1] + after[i]['bbox'][3])/2]
                    shift = np.sqrt((after_center[0] - before_center[0])**2 +
                                  (after_center[1] - before_center[1])**2)
                    bbox_shifts.append(shift)

                avg_shift = np.mean(bbox_shifts)
                info += f"位置校正:\n"
                info += f"• 平均位置偏移校正: {avg_shift:.1f}像素\n"

        return info
    
    def correct_detections(self, detections: List[Dict]) -> List[Dict]:
        """
        校正检测结果，减少误差
        
        Args:
            detections: 包含误差的检测结果
            
        Returns:
            校正后的检测结果
        """
        # 简单的校正策略：计算同类物体的平均位置
        class_detections = {}
        for det in detections:
            class_name = det['class_name']
            if class_name not in class_detections:
                class_detections[class_name] = []
            class_detections[class_name].append(det)
        
        corrected_detections = []
        for class_name, dets in class_detections.items():
            if len(dets) > 1:
                # 同类物体有多个检测结果，合并它们
                boxes = np.array([det['bbox'] for det in dets])
                
                # 计算平均边界框
                avg_x1 = np.mean(boxes[:, 0])
                avg_y1 = np.mean(boxes[:, 1])
                avg_x2 = np.mean(boxes[:, 2])
                avg_y2 = np.mean(boxes[:, 3])
                
                # 计算平均置信度
                confidences = np.array([det['confidence'] for det in dets])
                avg_confidence = np.mean(confidences)
                
                corrected_detections.append({
                    'class_id': dets[0]['class_id'],
                    'class_name': class_name,
                    'confidence': avg_confidence,
                    'bbox': [avg_x1, avg_y1, avg_x2, avg_y2]
                })
            else:
                # 只有一个检测结果，保持不变
                corrected_detections.extend(dets)
        
        return corrected_detections

class ImageProcessingWorker(QThread):
    """图像处理工作线程"""
    image_loaded = pyqtSignal(str, object)  # 模态类型, 图像数据

    def __init__(self, image_path: str, modality_type: str):
        super().__init__()
        self.image_path = image_path
        self.modality_type = modality_type

    def run(self):
        try:
            image = cv2.imread(self.image_path)
            if image is not None:
                self.image_loaded.emit(self.modality_type, image)
            else:
                print(f"无法加载{self.modality_type}图像: {self.image_path}")
        except Exception as e:
            print(f"加载{self.modality_type}图像时出错: {e}")

class FeatureExtractionWorker(QThread):
    """特征提取工作线程"""
    features_extracted = pyqtSignal(str, object)  # 模态类型, 特征数据

    def __init__(self, image: np.ndarray, modality_type: str):
        super().__init__()
        self.image = image
        self.modality_type = modality_type

    def run(self):
        try:
            extractor = MultiDomainFeatureExtractor()

            if self.modality_type == 'visible':
                features = extractor.extract_visible_features(self.image)
            elif self.modality_type == 'thermal':
                features = extractor.extract_thermal_features(self.image)
            elif self.modality_type == 'sar':
                features = extractor.extract_sar_features(self.image)
            else:
                features = {}

            self.features_extracted.emit(self.modality_type, features)
        except Exception as e:
            print(f"提取{self.modality_type}特征时出错: {e}")

class SceneClassificationWorker(QThread):
    """场景分类工作线程"""
    scene_classified = pyqtSignal(str, str)  # 分类结果, 解释

    def __init__(self, visible_features: Dict, thermal_features: Dict, sar_features: Dict):
        super().__init__()
        self.visible_features = visible_features
        self.thermal_features = thermal_features
        self.sar_features = sar_features

    def run(self):
        try:
            classifier = EnhancedSceneClassifier()
            result, explanation = classifier.classify_scene(
                self.visible_features, self.thermal_features, self.sar_features)
            self.scene_classified.emit(result, explanation)
        except Exception as e:
            print(f"场景分类时出错: {e}")

class DetectionWorker(QThread):
    """目标检测工作线程"""
    detection_complete = pyqtSignal(object)  # 检测结果字典

    def __init__(self, detector, image: np.ndarray, regime_info: Dict):
        super().__init__()
        self.detector = detector
        self.image = image
        self.regime_info = regime_info

    def run(self):
        try:
            # 执行带策略调整的检测
            detection_results = self.detector.detect_with_strategy_adjustment(
                self.image, self.regime_info)
            self.detection_complete.emit(detection_results)
        except Exception as e:
            print(f"目标检测时出错: {e}")

class DataSaveWorker(QThread):
    """数据保存工作线程"""
    save_complete = pyqtSignal(str)  # 保存路径

    def __init__(self, data: Dict, save_path: str):
        super().__init__()
        self.data = data
        self.save_path = save_path

    def run(self):
        try:
            # 确保目录存在
            os.makedirs(os.path.dirname(self.save_path), exist_ok=True)

            # 保存数据
            with open(self.save_path, 'w', encoding='utf-8') as f:
                json.dump(self.data, f, ensure_ascii=False, indent=2, default=str)

            self.save_complete.emit(self.save_path)
        except Exception as e:
            print(f"保存数据时出错: {e}")

class RealTimeDataSaver:
    """实时数据保存管理器"""

    def __init__(self, base_path: str = "process_data"):
        self.base_path = base_path
        self.session_id = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
        self.session_path = os.path.join(base_path, f"session_{self.session_id}")
        self.mutex = QMutex()

        # 创建会话目录
        os.makedirs(self.session_path, exist_ok=True)

        # 初始化日志文件
        self.log_file = os.path.join(self.session_path, "process_log.txt")
        self.write_log("会话开始", "系统初始化")

    def save_features(self, modality: str, features: Dict):
        """保存特征数据"""
        with QMutexLocker(self.mutex):
            try:
                features_path = os.path.join(self.session_path, f"{modality}_features.json")

                # 添加时间戳
                save_data = {
                    'timestamp': datetime.datetime.now().isoformat(),
                    'modality': modality,
                    'features': features
                }

                with open(features_path, 'w', encoding='utf-8') as f:
                    json.dump(save_data, f, ensure_ascii=False, indent=2, default=str)

                self.write_log("特征保存", f"{modality}模态特征已保存到{features_path}")

            except Exception as e:
                self.write_log("错误", f"保存{modality}特征时出错: {e}")

    def save_scene_result(self, scene_result: str, explanation: str):
        """保存场景识别结果"""
        with QMutexLocker(self.mutex):
            try:
                scene_path = os.path.join(self.session_path, "scene_classification.json")

                save_data = {
                    'timestamp': datetime.datetime.now().isoformat(),
                    'scene_result': scene_result,
                    'explanation': explanation
                }

                with open(scene_path, 'w', encoding='utf-8') as f:
                    json.dump(save_data, f, ensure_ascii=False, indent=2)

                self.write_log("场景识别", f"场景识别结果: {scene_result}")

            except Exception as e:
                self.write_log("错误", f"保存场景识别结果时出错: {e}")

    def save_detection_regime(self, regime_info: Dict):
        """保存探测体制信息"""
        with QMutexLocker(self.mutex):
            try:
                regime_path = os.path.join(self.session_path, "detection_regime.json")

                save_data = {
                    'timestamp': datetime.datetime.now().isoformat(),
                    'regime_info': regime_info
                }

                with open(regime_path, 'w', encoding='utf-8') as f:
                    json.dump(save_data, f, ensure_ascii=False, indent=2, default=str)

                self.write_log("探测体制", f"探测体制: {regime_info.get('regime_type', '未知')}")

            except Exception as e:
                self.write_log("错误", f"保存探测体制信息时出错: {e}")

    def save_detection_results(self, detection_results: Dict):
        """保存目标检测结果"""
        with QMutexLocker(self.mutex):
            try:
                detection_path = os.path.join(self.session_path, "detection_results.json")

                save_data = {
                    'timestamp': datetime.datetime.now().isoformat(),
                    'detection_results': detection_results
                }

                with open(detection_path, 'w', encoding='utf-8') as f:
                    json.dump(save_data, f, ensure_ascii=False, indent=2, default=str)

                before_count = len(detection_results.get('before_adjustment', []))
                after_count = len(detection_results.get('after_adjustment', []))
                self.write_log("目标检测", f"检测完成 - 调整前:{before_count}个, 调整后:{after_count}个")

            except Exception as e:
                self.write_log("错误", f"保存检测结果时出错: {e}")

    def save_bbox_analysis(self, bbox_data: Dict):
        """保存锚框区域分析数据"""
        with QMutexLocker(self.mutex):
            try:
                bbox_path = os.path.join(self.session_path, "bbox_analysis.json")

                save_data = {
                    'timestamp': datetime.datetime.now().isoformat(),
                    'bbox_analysis': bbox_data
                }

                with open(bbox_path, 'w', encoding='utf-8') as f:
                    json.dump(save_data, f, ensure_ascii=False, indent=2, default=str)

                self.write_log("锚框分析", f"锚框区域分析数据已保存")

            except Exception as e:
                self.write_log("错误", f"保存锚框分析数据时出错: {e}")

    def write_log(self, category: str, message: str):
        """写入处理日志"""
        try:
            timestamp = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            log_entry = f"[{timestamp}] [{category}] {message}\n"

            with open(self.log_file, 'a', encoding='utf-8') as f:
                f.write(log_entry)

        except Exception as e:
            print(f"写入日志时出错: {e}")

    def get_session_summary(self) -> Dict:
        """获取会话摘要"""
        summary = {
            'session_id': self.session_id,
            'session_path': self.session_path,
            'files': []
        }

        try:
            for file in os.listdir(self.session_path):
                file_path = os.path.join(self.session_path, file)
                if os.path.isfile(file_path):
                    summary['files'].append({
                        'name': file,
                        'size': os.path.getsize(file_path),
                        'modified': datetime.datetime.fromtimestamp(
                            os.path.getmtime(file_path)).isoformat()
                    })
        except Exception as e:
            print(f"获取会话摘要时出错: {e}")

        return summary

class KnowledgeBase:
    """知识库，存储各类别的本体知识"""
    
    def __init__(self):
        self.knowledge = {
            'person': '人是两足直立的哺乳动物，通常具有智能和社会性。在监控场景中，人是最常见的目标之一。',
            'bicycle': '自行车是一种两轮交通工具，通常由人力驱动。在城市和乡村环境中都很常见。',
            'car': '汽车是一种四轮机动车辆，通常用于运输乘客和货物。常见于道路和停车场。',
            'motorcycle': '摩托车是一种两轮机动车辆，速度快，机动性强。常见于城市和高速公路。',
            'airplane': '飞机是一种空中交通工具，通常用于长途旅行和货物运输。常见于机场和天空。',
            'bus': '巴士是一种大型公共交通工具，用于运输大量乘客。常见于城市公交线路。',
            'train': '火车是一种铁路交通工具，用于长途和短途运输。常见于火车站和铁路轨道。',
            'truck': '卡车是一种大型载货车辆，用于运输货物。常见于货运和物流场景。',
            'boat': '船是一种水上交通工具，用于在河流、湖泊和海洋中航行。',
            'traffic light': '交通灯是一种交通控制设备，用于指挥车辆和行人的通行。',
            'fire hydrant': '消防栓是一种消防设备，用于提供灭火用水。常见于城市街道。',
            'stop sign': '停车标志是一种交通标志，指示车辆必须停车。',
            'parking meter': '停车计时器是一种收费设备，用于计时停车。常见于城市停车场。',
            'bench': '长椅是一种供人休息的座位，常见于公园、广场和街道。',
            'bird': '鸟是一种有羽毛的脊椎动物，通常会飞行。常见于自然环境中。',
            'cat': '猫是一种家养宠物，属于猫科动物。性格独立，喜欢玩耍。',
            'dog': '狗是一种家养宠物，属于犬科动物。通常忠诚，可训练性强。',
            'horse': '马是一种大型哺乳动物，通常用于骑乘和运输。',
            'sheep': '羊是一种家养动物，主要用于生产羊毛和肉类。',
            'cow': '牛是一种家养动物，主要用于生产牛奶和肉类。',
            'elephant': '大象是一种大型哺乳动物，是陆地上最大的动物。具有长鼻子和大耳朵。',
            'bear': '熊是一种大型哺乳动物，通常生活在森林中。体型庞大，力量强大。',
            'zebra': '斑马是一种非洲哺乳动物，具有独特的黑白条纹。',
            'giraffe': '长颈鹿是一种非洲哺乳动物，以其超长的脖子和高大的身材而闻名。',
            'backpack': '背包是一种用于携带物品的袋子，通常背在背上。常见于旅行和上学。',
            'umbrella': '雨伞是一种防雨工具，由伞骨和伞面组成。',
            'handbag': '手提包是一种用于携带个人物品的袋子，通常由女性使用。',
            'tie': '领带是一种男性服饰，通常系在衬衫领口。',
            'suitcase': '行李箱是一种用于旅行的大型箱子，用于存放衣物和物品。',
            'frisbee': '飞盘是一种投掷玩具，通常由塑料制成。',
            'skis': '滑雪板是一种冬季运动装备，用于在雪地上滑行。',
            'snowboard': '滑雪板是一种冬季运动装备，用于在雪地上滑行，通常比滑雪板更宽。',
            'sports ball': '运动球是一种球类运动器材，如足球、篮球、网球等。',
            'kite': '风筝是一种玩具，通过风力在空中飞行。',
            'baseball bat': '棒球棒是一种棒球运动器材，用于击打棒球。',
            'baseball glove': '棒球手套是一种棒球运动器材，用于接住棒球。',
            'skateboard': '滑板是一种运动器材，用于在地面上滑行。',
            'surfboard': '冲浪板是一种水上运动器材，用于在海浪上滑行。',
            'tennis racket': '网球拍是一种网球运动器材，用于击打网球。',
            'bottle': '瓶子是一种容器，通常用于存放液体。',
            'wine glass': '葡萄酒杯是一种玻璃容器，用于盛装葡萄酒。',
            'cup': '杯子是一种容器，通常用于盛装液体，如茶、咖啡等。',
            'fork': '叉子是一种餐具，用于叉取食物。',
            'knife': '刀是一种餐具，用于切割食物。',
            'spoon': '勺子是一种餐具，用于舀取食物。',
            'bowl': '碗是一种容器，通常用于盛装食物。',
            'banana': '香蕉是一种水果，黄色外皮，柔软果肉。',
            'apple': '苹果是一种水果，通常有红色、绿色或黄色外皮。',
            'sandwich': '三明治是一种食品，通常由两片面包夹着馅料组成。',
            'orange': '橙子是一种水果，橙色外皮，多汁果肉。',
            'broccoli': '西兰花是一种蔬菜，绿色花球，营养丰富。',
            'carrot': '胡萝卜是一种蔬菜，橙色根茎，富含维生素A。',
            'hot dog': '热狗是一种食品，通常由香肠和面包组成。',
            'pizza': '披萨是一种意大利食品，由面团、番茄酱和各种配料组成。',
            'donut': '甜甜圈是一种甜点，通常由油炸面团制成，表面有糖霜。',
            'cake': '蛋糕是一种甜点，通常由面粉、糖和鸡蛋制成。',
            'chair': '椅子是一种家具，用于坐人。',
            'couch': '沙发是一种家具，通常用于多人坐卧。',
            'potted plant': '盆栽植物是一种室内装饰，通常种植在花盆中。',
            'bed': '床是一种家具，用于睡觉和休息。',
            'dining table': '餐桌是一种家具，用于用餐。',
            'toilet': '马桶是一种卫生设备，用于排泄和冲洗。',
            'tv': '电视是一种电子设备，用于接收和播放电视节目。',
            'laptop': '笔记本电脑是一种便携式电子设备，用于工作和娱乐。',
            'mouse': '鼠标是一种计算机输入设备，用于控制光标。',
            'remote': '遥控器是一种电子设备，用于远程控制其他设备。',
            'keyboard': '键盘是一种计算机输入设备，用于输入文字和命令。',
            'cell phone': '手机是一种便携式通信设备，用于打电话和上网。',
            'microwave': '微波炉是一种厨房电器，用于加热和烹饪食物。',
            'oven': '烤箱是一种厨房电器，用于烘焙和烤制食物。',
            'toaster': '烤面包机是一种厨房电器，用于烤面包。',
            'sink': '水槽是一种厨房设备，用于洗涤餐具和食物。',
            'refrigerator': '冰箱是一种厨房电器，用于冷藏和冷冻食物。',
            'book': '书是一种出版物，由纸张和封面组成，用于存储和传播知识。',
            'clock': '时钟是一种计时设备，用于显示时间。',
            'vase': '花瓶是一种容器，通常用于插花。',
            'scissors': '剪刀是一种切割工具，由两个刀片组成。',
            'teddy bear': '泰迪熊是一种毛绒玩具，通常模仿熊的形象。',
            'hair drier': '吹风机是一种电器，用于吹干头发。',
            'toothbrush': '牙刷是一种清洁工具，用于刷牙。'
        }
    
    def get_knowledge(self, class_name: str) -> str:
        """获取指定类别的本体知识"""
        return self.knowledge.get(class_name, f"关于'{class_name}'的知识暂未录入。")

class ImageWidget(QWidget):
    """用于显示图像和检测结果的自定义控件"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.image = None
        self.detections = []
        self.title = ""
        self.font_scale = 0.5
        
        self.initUI()
    
    def initUI(self):
        self.layout = QVBoxLayout(self)
        
        self.title_label = QLabel(self.title)
        self.title_label.setAlignment(Qt.AlignCenter)
        self.title_label.setFont(QFont("SimHei", 12, QFont.Bold))
        self.layout.addWidget(self.title_label)
        
        self.image_label = QLabel()
        self.image_label.setAlignment(Qt.AlignCenter)
        self.image_label.setMinimumSize(100, 100)
        self.layout.addWidget(self.image_label)
        
        self.setLayout(self.layout)

    def setTitle(self, title: str):
        """设置标题"""
        self.title = title
        self.title_label.setText(title)

    def set_image(self, image: np.ndarray, detections: List[Dict], title: str, font_scale: float = 0.5):
        """设置要显示的图像和检测结果"""
        self.image = image.copy()
        self.detections = detections
        self.title = title
        self.font_scale = font_scale
        
        self.update_image()
    
    def update_image(self):
        """更新显示的图像，包括绘制边界框和标签"""
        if self.image is None:
            return
        
        # 复制图像，避免修改原图
        display_image = self.image.copy()
        
        # 绘制检测结果
        for det in self.detections:
            x1, y1, x2, y2 = map(int, det['bbox'])
            class_name = det['class_name']
            confidence = det['confidence']
            
            # 绘制边界框
            color = (0, 255, 0)  # 绿色
            cv2.rectangle(display_image, (x1, y1), (x2, y2), color, 2)
            
            # 绘制标签背景
            label = f"{class_name}: {confidence:.2f}"
            (label_width, label_height), _ = cv2.getTextSize(label, cv2.FONT_HERSHEY_SIMPLEX, self.font_scale, 2)
            cv2.rectangle(display_image, (x1, y1 - label_height - 10), (x1 + label_width, y1), color, -1)
            
            # 绘制标签文本
            cv2.putText(display_image, label, (x1, y1 - 5), cv2.FONT_HERSHEY_SIMPLEX, self.font_scale, (0, 0, 0), 2)
        
        # 更新标题
        self.title_label.setText(self.title)
        
        # 转换为Qt格式并显示
        height, width = display_image.shape[:2]
        if len(display_image.shape) == 3:
            channels = display_image.shape[2]
            bytesPerLine = channels * width
            qImg = QImage(display_image.data, width, height, bytesPerLine, QImage.Format_BGR888)
        else:
            bytesPerLine = width
            qImg = QImage(display_image.data, width, height, bytesPerLine, QImage.Format_Grayscale8)
        self.image_label.setPixmap(QPixmap.fromImage(qImg).scaled(
            self.image_label.width(), self.image_label.height(), 
            Qt.KeepAspectRatio, Qt.SmoothTransformation))
    
    def resizeEvent(self, event):
        """窗口大小改变时重新绘制图像"""
        if self.image is not None:
            self.update_image()
        super().resizeEvent(event)

class FeatureDisplayWidget(QWidget):
    """用于显示特征提取结果的自定义控件"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.feature_type = ""
        self.features = {}
        
        self.initUI()
    
    def initUI(self):
        self.layout = QVBoxLayout(self)
        
        self.title_label = QLabel(self.feature_type)
        self.title_label.setAlignment(Qt.AlignCenter)
        self.title_label.setFont(QFont("SimHei", 12, QFont.Bold))
        self.layout.addWidget(self.title_label)
        
        self.feature_text = QTextEdit()
        self.feature_text.setReadOnly(True)
        self.feature_text.setFont(QFont("SimHei", 10))
        self.layout.addWidget(self.feature_text)
        
        self.setLayout(self.layout)
    
    def set_features(self, feature_type: str, features: Dict):
        """设置要显示的特征类型和特征值"""
        self.feature_type = feature_type
        self.features = features
        
        self.update_display()
    
    def update_display(self):
        """更新显示的特征信息"""
        self.title_label.setText(f"{self.feature_type}特征")
        
        # 格式化特征信息
        feature_text = ""
        for key, value in self.features.items():
            if key != 'feature_vector':  # 不显示特征向量，因为它是内部使用的
                feature_text += f"{key}: {value:.4f}\n"
        
        self.feature_text.setText(feature_text)

class MultiModalRecognitionApp(QMainWindow):
    """多模态图像识别应用主窗口"""
    
    def __init__(self):
        super().__init__()
        print("正在初始化多模态识别应用...")

        self.visible_image = None
        self.thermal_image = None
        self.sar_image = None

        self.visible_features = None
        self.thermal_features = None
        self.sar_features = None

        self.scene_result = ""
        self.scene_explanation = ""

        print("正在初始化目标检测器...")
        self.object_detector = EnhancedObjectDetector()
        print("正在初始化数据保存器...")
        self.data_saver = RealTimeDataSaver()
        print("正在初始化知识库...")
        self.knowledge_base = KnowledgeBase()

        self.error_level = 50  # 默认误差级别

        print("正在初始化用户界面...")
        self.initUI()
        print("应用程序初始化完成")
    
    def initUI(self):
        """初始化用户界面"""
        self.setWindowTitle("多模态图像识别系统")
        self.setGeometry(100, 100, 1200, 800)
        
        # 创建中央部件和主布局
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        main_layout = QVBoxLayout(central_widget)
        
        # 创建标签页控件
        self.tabs = QTabWidget()
        
        # 创建图像输入标签页
        self.create_input_tab()
        
        # 创建特征提取标签页
        self.create_feature_tab()
        
        # 创建场景识别标签页
        self.create_scene_tab()
        
        # 创建目标检测标签页
        self.create_detection_tab()
        
        # 将标签页添加到标签页控件
        self.tabs.addTab(self.input_tab, "图像输入")
        self.tabs.addTab(self.feature_tab, "特征提取")
        self.tabs.addTab(self.scene_tab, "场景识别")
        self.tabs.addTab(self.detection_tab, "目标检测")
        
        # 将标签页控件添加到主布局
        main_layout.addWidget(self.tabs)
    
    def create_input_tab(self):
        """创建图像输入标签页"""
        self.input_tab = QWidget()
        input_layout = QVBoxLayout(self.input_tab)
        
        # 创建图像选择区域
        selection_layout = QHBoxLayout()
        
        # 可见光图像选择
        visible_group = QGroupBox("可见光图像")
        visible_layout = QVBoxLayout(visible_group)
        
        self.visible_path_label = QLabel("未选择图像")
        visible_layout.addWidget(self.visible_path_label)
        
        visible_button_layout = QHBoxLayout()
        self.visible_select_button = QPushButton("选择图像")
        self.visible_select_button.clicked.connect(self.select_visible_image)
        visible_button_layout.addWidget(self.visible_select_button)
        
        self.visible_display_button = QPushButton("显示图像")
        self.visible_display_button.clicked.connect(self.display_visible_image)
        self.visible_display_button.setEnabled(False)
        visible_button_layout.addWidget(self.visible_display_button)
        
        visible_layout.addLayout(visible_button_layout)
        selection_layout.addWidget(visible_group)
        
        # 红外图像选择
        thermal_group = QGroupBox("红外图像")
        thermal_layout = QVBoxLayout(thermal_group)
        
        self.thermal_path_label = QLabel("未选择图像")
        thermal_layout.addWidget(self.thermal_path_label)
        
        thermal_button_layout = QHBoxLayout()
        self.thermal_select_button = QPushButton("选择图像")
        self.thermal_select_button.clicked.connect(self.select_thermal_image)
        thermal_button_layout.addWidget(self.thermal_select_button)
        
        self.thermal_display_button = QPushButton("显示图像")
        self.thermal_display_button.clicked.connect(self.display_thermal_image)
        self.thermal_display_button.setEnabled(False)
        thermal_button_layout.addWidget(self.thermal_display_button)
        
        thermal_layout.addLayout(thermal_button_layout)
        selection_layout.addWidget(thermal_group)
        
        # SAR图像选择
        sar_group = QGroupBox("SAR图像")
        sar_layout = QVBoxLayout(sar_group)
        
        self.sar_path_label = QLabel("未选择图像")
        sar_layout.addWidget(self.sar_path_label)
        
        sar_button_layout = QHBoxLayout()
        self.sar_select_button = QPushButton("选择图像")
        self.sar_select_button.clicked.connect(self.select_sar_image)
        sar_button_layout.addWidget(self.sar_select_button)
        
        self.sar_display_button = QPushButton("显示图像")
        self.sar_display_button.clicked.connect(self.display_sar_image)
        self.sar_display_button.setEnabled(False)
        sar_button_layout.addWidget(self.sar_display_button)
        
        sar_layout.addLayout(sar_button_layout)
        selection_layout.addWidget(sar_group)
        
        input_layout.addLayout(selection_layout)
        
        # 创建图像显示区域
        self.image_display_area = QWidget()
        image_display_layout = QHBoxLayout(self.image_display_area)
        
        self.visible_image_widget = ImageWidget()
        self.visible_image_widget.setTitle("可见光图像")
        image_display_layout.addWidget(self.visible_image_widget)
        
        self.thermal_image_widget = ImageWidget()
        self.thermal_image_widget.setTitle("红外图像")
        image_display_layout.addWidget(self.thermal_image_widget)
        
        self.sar_image_widget = ImageWidget()
        self.sar_image_widget.setTitle("SAR图像")
        image_display_layout.addWidget(self.sar_image_widget)
        
        input_layout.addWidget(self.image_display_area)
        
        # 创建特征提取按钮
        self.feature_extract_button = QPushButton("提取特征")
        self.feature_extract_button.clicked.connect(self.extract_features)
        self.feature_extract_button.setEnabled(False)
        input_layout.addWidget(self.feature_extract_button)
    
    def create_feature_tab(self):
        """创建特征提取标签页"""
        self.feature_tab = QWidget()
        feature_layout = QVBoxLayout(self.feature_tab)
        
        # 创建特征显示区域
        feature_display_layout = QHBoxLayout()
        
        self.visible_feature_widget = FeatureDisplayWidget()
        feature_display_layout.addWidget(self.visible_feature_widget)
        
        self.thermal_feature_widget = FeatureDisplayWidget()
        feature_display_layout.addWidget(self.thermal_feature_widget)
        
        self.sar_feature_widget = FeatureDisplayWidget()
        feature_display_layout.addWidget(self.sar_feature_widget)
        
        feature_layout.addLayout(feature_display_layout)
        
        # 创建场景识别按钮
        self.scene_recognition_button = QPushButton("场景识别")
        self.scene_recognition_button.clicked.connect(self.recognize_scene)
        self.scene_recognition_button.setEnabled(False)
        feature_layout.addWidget(self.scene_recognition_button)
    
    def create_scene_tab(self):
        """创建场景识别标签页"""
        self.scene_tab = QWidget()
        scene_layout = QVBoxLayout(self.scene_tab)
        
        # 创建场景识别结果显示区域
        self.scene_result_label = QLabel("场景识别结果将显示在这里")
        self.scene_result_label.setAlignment(Qt.AlignCenter)
        self.scene_result_label.setFont(QFont("SimHei", 16, QFont.Bold))
        scene_layout.addWidget(self.scene_result_label)
        
        # 创建场景识别依据显示区域
        self.scene_explanation_text = QTextEdit()
        self.scene_explanation_text.setReadOnly(True)
        self.scene_explanation_text.setFont(QFont("SimHei", 10))
        scene_layout.addWidget(self.scene_explanation_text)
        
        # 创建目标检测按钮
        self.detection_button = QPushButton("目标检测")
        self.detection_button.clicked.connect(self.perform_detection)
        self.detection_button.setEnabled(False)
        scene_layout.addWidget(self.detection_button)
    
    def create_detection_tab(self):
        """创建目标检测标签页"""
        self.detection_tab = QWidget()
        detection_layout = QVBoxLayout(self.detection_tab)
        
        # 创建误差级别控制
        error_layout = QHBoxLayout()
        error_label = QLabel("识别误差级别:")
        error_layout.addWidget(error_label)
        
        self.error_slider = QSlider(Qt.Horizontal)
        self.error_slider.setMinimum(0)
        self.error_slider.setMaximum(100)
        self.error_slider.setValue(self.error_level)
        self.error_slider.valueChanged.connect(self.set_error_level)
        error_layout.addWidget(self.error_slider)
        
        self.error_value_label = QLabel(f"{self.error_level}")
        self.error_value_label.setFixedWidth(50)
        error_layout.addWidget(self.error_value_label)
        
        detection_layout.addLayout(error_layout)
        
        # 创建检测结果显示区域
        detection_display_layout = QHBoxLayout()
        
        self.before_correction_widget = ImageWidget()
        self.before_correction_widget.setTitle("识别策略引入前")
        detection_display_layout.addWidget(self.before_correction_widget)
        
        self.after_correction_widget = ImageWidget()
        self.after_correction_widget.setTitle("识别策略引入后")
        detection_display_layout.addWidget(self.after_correction_widget)
        
        detection_layout.addLayout(detection_display_layout)
        
        # 创建选中对象信息显示区域
        info_layout = QHBoxLayout()
        
        self.selected_object_widget = ImageWidget()
        self.selected_object_widget.setTitle("选中对象")
        info_layout.addWidget(self.selected_object_widget)
        
        self.object_info_text = QTextEdit()
        self.object_info_text.setReadOnly(True)
        self.object_info_text.setFont(QFont("SimHei", 10))
        info_layout.addWidget(self.object_info_text)
        
        detection_layout.addLayout(info_layout)
    
    def select_visible_image(self):
        """选择可见光图像"""
        options = QFileDialog.Options()
        file_name, _ = QFileDialog.getOpenFileName(
            self, "选择可见光图像", "", "图像文件 (*.png *.jpg *.jpeg *.bmp);;所有文件 (*)", options=options)
        
        if file_name:
            self.visible_image_path = file_name
            self.visible_path_label.setText(os.path.basename(file_name))
            self.visible_display_button.setEnabled(True)
            
            # 检查是否所有图像都已选择
            self.check_all_images_selected()
    
    def select_thermal_image(self):
        """选择红外图像"""
        options = QFileDialog.Options()
        file_name, _ = QFileDialog.getOpenFileName(
            self, "选择红外图像", "", "图像文件 (*.png *.jpg *.jpeg *.bmp);;所有文件 (*)", options=options)
        
        if file_name:
            self.thermal_image_path = file_name
            self.thermal_path_label.setText(os.path.basename(file_name))
            self.thermal_display_button.setEnabled(True)
            
            # 检查是否所有图像都已选择
            self.check_all_images_selected()
    
    def select_sar_image(self):
        """选择SAR图像"""
        options = QFileDialog.Options()
        file_name, _ = QFileDialog.getOpenFileName(
            self, "选择SAR图像", "", "图像文件 (*.png *.jpg *.jpeg *.bmp);;所有文件 (*)", options=options)
        
        if file_name:
            self.sar_image_path = file_name
            self.sar_path_label.setText(os.path.basename(file_name))
            self.sar_display_button.setEnabled(True)
            
            # 检查是否所有图像都已选择
            self.check_all_images_selected()
    
    def check_all_images_selected(self):
        """检查是否所有类型的图像都已选择"""
        if hasattr(self, 'visible_image_path') and hasattr(self, 'thermal_image_path') and hasattr(self, 'sar_image_path'):
            self.feature_extract_button.setEnabled(True)
    
    def display_visible_image(self):
        """显示可见光图像"""
        self.visible_image = cv2.imread(self.visible_image_path)
        if self.visible_image is not None:
            self.visible_image_widget.set_image(self.visible_image, [], "可见光图像")
        else:
            print(f"无法加载可见光图像: {self.visible_image_path}")

    def display_thermal_image(self):
        """显示红外图像"""
        self.thermal_image = cv2.imread(self.thermal_image_path)
        if self.thermal_image is not None:
            self.thermal_image_widget.set_image(self.thermal_image, [], "红外图像")
        else:
            print(f"无法加载红外图像: {self.thermal_image_path}")

    def display_sar_image(self):
        """显示SAR图像"""
        self.sar_image = cv2.imread(self.sar_image_path)
        if self.sar_image is not None:
            self.sar_image_widget.set_image(self.sar_image, [], "SAR图像")
        else:
            print(f"无法加载SAR图像: {self.sar_image_path}")
    
    def extract_features(self):
        """提取三种图像的特征"""
        # 首先加载图像（如果还没有加载）
        if not hasattr(self, 'visible_image') or self.visible_image is None:
            self.display_visible_image()
        if not hasattr(self, 'thermal_image') or self.thermal_image is None:
            self.display_thermal_image()
        if not hasattr(self, 'sar_image') or self.sar_image is None:
            self.display_sar_image()

        if self.visible_image is not None and self.thermal_image is not None and self.sar_image is not None:
            try:
                # 使用多线程提取特征
                self.feature_workers = []

                # 可见光特征提取
                visible_worker = FeatureExtractionWorker(self.visible_image, 'visible')
                visible_worker.features_extracted.connect(self.on_features_extracted)
                self.feature_workers.append(visible_worker)
                visible_worker.start()

                # 红外特征提取
                thermal_worker = FeatureExtractionWorker(self.thermal_image, 'thermal')
                thermal_worker.features_extracted.connect(self.on_features_extracted)
                self.feature_workers.append(thermal_worker)
                thermal_worker.start()

                # SAR特征提取
                sar_worker = FeatureExtractionWorker(self.sar_image, 'sar')
                sar_worker.features_extracted.connect(self.on_features_extracted)
                self.feature_workers.append(sar_worker)
                sar_worker.start()

                # 初始化特征计数器
                self.features_completed = 0
                self.total_features = 3

            except Exception as e:
                print(f"特征提取失败: {e}")
        else:
            print("请确保所有图像都已正确加载")

    def on_features_extracted(self, modality_type: str, features: Dict):
        """特征提取完成回调"""
        try:
            # 保存特征
            if modality_type == 'visible':
                self.visible_features = features
            elif modality_type == 'thermal':
                self.thermal_features = features
            elif modality_type == 'sar':
                self.sar_features = features

            # 实时保存特征数据
            self.data_saver.save_features(modality_type, features)

            # 更新计数器
            self.features_completed += 1

            # 更新显示
            self.update_feature_display()

            # 如果所有特征都提取完成，开始场景识别
            if self.features_completed >= self.total_features:
                self.start_scene_classification()

        except Exception as e:
            print(f"处理{modality_type}特征时出错: {e}")

    def start_scene_classification(self):
        """开始场景分类"""
        if (self.visible_features is not None and
            self.thermal_features is not None and
            self.sar_features is not None):

            # 使用多线程进行场景分类
            self.scene_worker = SceneClassificationWorker(
                self.visible_features, self.thermal_features, self.sar_features)
            self.scene_worker.scene_classified.connect(self.on_scene_classified)
            self.scene_worker.start()
    
    def on_scene_classified(self, scene_result: str, explanation: str):
        """场景分类完成回调"""
        try:
            self.scene_result = scene_result
            self.scene_explanation = explanation

            # 实时保存场景识别结果
            self.data_saver.save_scene_result(scene_result, explanation)

            # 更新显示
            self.update_scene_display()

            # 开始探测体制选择
            self.start_detection_regime_selection()

        except Exception as e:
            print(f"处理场景分类结果时出错: {e}")

    def start_detection_regime_selection(self):
        """开始探测体制选择"""
        try:
            # 确定可用模态
            available_modalities = []
            if self.visible_image is not None:
                available_modalities.append('visible')
            if self.thermal_image is not None:
                available_modalities.append('thermal')
            if self.sar_image is not None:
                available_modalities.append('sar')

            # 选择探测体制
            self.regime_info = DetectionRegimeSelector.determine_detection_regime(
                available_modalities, self.scene_result)

            # 实时保存探测体制信息
            self.data_saver.save_detection_regime(self.regime_info)

            # 更新显示
            self.update_regime_display()

            # 开始目标检测
            self.start_object_detection()

        except Exception as e:
            print(f"探测体制选择时出错: {e}")

    def start_object_detection(self):
        """开始目标检测"""
        try:
            # 选择主要图像进行检测（根据权重）
            primary_modality = self.regime_info.get('primary_modality', 'visible')

            if primary_modality == 'visible' and self.visible_image is not None:
                detection_image = self.visible_image
            elif primary_modality == 'thermal' and self.thermal_image is not None:
                detection_image = self.thermal_image
            elif primary_modality == 'sar' and self.sar_image is not None:
                detection_image = self.sar_image
            else:
                detection_image = self.visible_image  # 默认使用可见光

            # 使用多线程进行目标检测
            self.detection_worker = DetectionWorker(
                self.object_detector, detection_image, self.regime_info)
            self.detection_worker.detection_complete.connect(self.on_detection_complete)
            self.detection_worker.start()

        except Exception as e:
            print(f"开始目标检测时出错: {e}")

    def on_detection_complete(self, detection_results: Dict):
        """目标检测完成回调"""
        try:
            self.detection_results = detection_results

            # 实时保存检测结果
            self.data_saver.save_detection_results(detection_results)

            # 更新显示
            self.update_detection_display()

            # 进行锚框区域分析
            self.analyze_bbox_regions()

        except Exception as e:
            print(f"处理检测结果时出错: {e}")

    def recognize_scene(self):
        """识别场景（保留原方法以兼容UI）"""
        if (self.visible_features is not None and
            self.thermal_features is not None and
            self.sar_features is not None):
            self.start_scene_classification()
            
            # 启用目标检测按钮
            self.detection_button.setEnabled(True)
            
            # 切换到场景标签页
            self.tabs.setCurrentIndex(2)
    
    def set_error_level(self, value):
        """设置误差级别"""
        self.error_level = value
        self.error_value_label.setText(f"{value}")

    def analyze_bbox_regions(self):
        """分析锚框区域"""
        try:
            if not hasattr(self, 'detection_results') or not self.detection_results:
                return

            bbox_analysis = {}
            after_detections = self.detection_results.get('after_adjustment', [])

            for i, detection in enumerate(after_detections):
                bbox = detection['bbox']

                # 提取锚框区域
                if self.thermal_image is not None:
                    x1, y1, x2, y2 = [int(coord) for coord in bbox]
                    x1, y1 = max(0, x1), max(0, y1)
                    x2 = min(self.thermal_image.shape[1], x2)
                    y2 = min(self.thermal_image.shape[0], y2)

                    if x2 > x1 and y2 > y1:
                        bbox_region = self.thermal_image[y1:y2, x1:x2]

                        if bbox_region.size > 0:
                            # 计算梯度特征
                            if len(bbox_region.shape) == 3:
                                gray_region = cv2.cvtColor(bbox_region, cv2.COLOR_BGR2GRAY)
                            else:
                                gray_region = bbox_region

                            grad_x = cv2.Sobel(gray_region, cv2.CV_64F, 1, 0, ksize=3)
                            grad_y = cv2.Sobel(gray_region, cv2.CV_64F, 0, 1, ksize=3)
                            gradient_magnitude = np.sqrt(grad_x**2 + grad_y**2)

                            bbox_analysis[f'bbox_{i}'] = {
                                'bbox': bbox,
                                'class_name': detection['class_name'],
                                'confidence': detection['confidence'],
                                'region_size': bbox_region.shape,
                                'mean_gradient': np.mean(gradient_magnitude),
                                'max_gradient': np.max(gradient_magnitude),
                                'gradient_std': np.std(gradient_magnitude)
                            }

            # 保存锚框分析数据
            self.data_saver.save_bbox_analysis(bbox_analysis)

            # 更新锚框分析显示
            self.update_bbox_analysis_display(bbox_analysis)

        except Exception as e:
            print(f"锚框区域分析时出错: {e}")

    def update_regime_display(self):
        """更新探测体制显示"""
        if hasattr(self, 'regime_info') and self.regime_info:
            regime_text = self.regime_info.get('description', '探测体制信息不可用')
            print("探测体制信息:")
            print(regime_text)

    def update_detection_display(self):
        """更新检测结果显示"""
        if hasattr(self, 'detection_results') and self.detection_results:
            adjustment_info = self.detection_results.get('adjustment_info', '检测信息不可用')
            print("检测结果:")
            print(adjustment_info)

    def update_bbox_analysis_display(self, bbox_analysis: Dict):
        """更新锚框分析显示"""
        print("锚框区域分析:")
        for bbox_id, analysis in bbox_analysis.items():
            print(f"{bbox_id}: {analysis['class_name']} (置信度: {analysis['confidence']:.3f})")
            print(f"  梯度特征 - 平均: {analysis['mean_gradient']:.2f}, 最大: {analysis['max_gradient']:.2f}")

    def update_feature_display(self):
        """更新特征显示"""
        try:
            if hasattr(self, 'visible_features') and self.visible_features:
                self.visible_feature_widget.set_features("可见光", self.visible_features)
            if hasattr(self, 'thermal_features') and self.thermal_features:
                self.thermal_feature_widget.set_features("红外", self.thermal_features)
            if hasattr(self, 'sar_features') and self.sar_features:
                self.sar_feature_widget.set_features("SAR", self.sar_features)

            # 如果所有特征都已提取，切换到特征标签页
            if (hasattr(self, 'features_completed') and
                self.features_completed >= self.total_features):
                self.tabs.setCurrentIndex(1)
                self.scene_recognition_button.setEnabled(True)

        except Exception as e:
            print(f"更新特征显示时出错: {e}")

    def update_scene_display(self):
        """更新场景显示"""
        try:
            if hasattr(self, 'scene_result') and hasattr(self, 'scene_explanation'):
                self.scene_result_label.setText(f"场景识别结果: {self.scene_result}")
                self.scene_explanation_text.setText(self.scene_explanation)
                self.tabs.setCurrentIndex(2)
                self.detection_button.setEnabled(True)
        except Exception as e:
            print(f"更新场景显示时出错: {e}")
    
    def perform_detection(self):
        """执行目标检测"""
        if self.visible_image is not None:
            # 创建工作线程执行目标检测
            self.detection_worker = DetectionWorker(self.object_detector, self.visible_image, self.error_level)
            self.detection_worker.detection_complete.connect(self.on_detection_complete)
            self.detection_worker.start()
    
    def on_detection_complete(self, detections_with_error, corrected_detections, original_image):
        """处理目标检测完成事件"""
        # 显示检测结果
        self.before_correction_widget.set_image(original_image, detections_with_error, "识别策略引入前", 0.7)
        self.after_correction_widget.set_image(original_image, corrected_detections, "识别策略引入后", 0.7)
        
        # 切换到目标检测标签页
        self.tabs.setCurrentIndex(3)

if __name__ == "__main__":
    print("正在启动多模态图像识别系统...")
    app = QApplication(sys.argv)
    print("QApplication创建成功")

    app.setStyle('Fusion')  # 使用Fusion风格，看起来更现代
    print("应用样式设置完成")

    # 设置中文字体
    try:
        font = QFont("SimHei")
        app.setFont(font)
        print("字体设置完成")
    except Exception as e:
        print(f"字体设置失败: {e}")

    print("正在创建主窗口...")
    window = MultiModalRecognitionApp()
    print("正在显示窗口...")
    window.show()
    print("进入事件循环...")
    sys.exit(app.exec_())
