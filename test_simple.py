import sys
import os
from PyQt5.QtWidgets import QApplication, QMainWindow, QLabel, QPushButton, QVBoxLayout, QWidget
from PyQt5.QtCore import Qt

class SimpleTestApp(QMainWindow):
    def __init__(self):
        super().__init__()
        self.initUI()
    
    def initUI(self):
        self.setWindowTitle("简单测试应用")
        self.setGeometry(100, 100, 400, 300)
        
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        layout = QVBoxLayout(central_widget)
        
        label = QLabel("应用程序正在运行！")
        label.setAlignment(Qt.AlignCenter)
        layout.addWidget(label)
        
        button = QPushButton("点击测试")
        button.clicked.connect(self.on_button_click)
        layout.addWidget(button)
    
    def on_button_click(self):
        print("按钮被点击了！")

if __name__ == "__main__":
    print("正在启动应用程序...")
    app = QApplication(sys.argv)
    print("QApplication 创建成功")
    
    window = SimpleTestApp()
    print("窗口创建成功")
    
    window.show()
    print("窗口显示成功")
    
    print("进入事件循环...")
    sys.exit(app.exec_())
