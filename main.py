from model.feature_img import *
from model.feature_video import *

if __name__ == "__main__":
    # 单张图片（视频）
    img = r"data\image\image_2_450.jpg"
    video = r"data\video\output.mp4"
    # 多张图片
    img_list = [
        r"data\image\image_2_457.jpg",
        r"data\image\image_2_493.jpg"
    ]

    output_folder1 = r"data\results\output1"
    output_folder2 = r"data\results\output2"
    output_folder3 = r"data\results\output3" 

#########调用方法##########
    # 图像
    ImageProcessor.process_ImageProcessor(img, output_folder1)#单一图像
    a = FeatureExtractor.process_FeatureExtractor(output_folder1, output_folder2) #设计暂定为输入文件夹
    print(a)





