#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
多模态识别系统启动脚本
"""

import sys
import os

# 添加当前目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

try:
    from multimodal_recognition_system import main
    
    if __name__ == "__main__":
        print("=" * 60)
        print("多模态识别系统")
        print("=" * 60)
        print("功能特点:")
        print("1. 自动识别可见光、红外、SAR图像/视频")
        print("2. 多域特征提取（空域、频域、小波域）")
        print("3. 智能场景识别（白天/黑夜）")
        print("4. 多探测体制融合识别")
        print("5. YOLO目标检测与策略调整")
        print("6. 红外梯度特征可视化")
        print("7. 实时数据保存与多线程优化")
        print("=" * 60)
        print("正在启动系统...")
        
        # 启动主程序
        main()
        
except ImportError as e:
    print(f"导入错误: {e}")
    print("请确保已安装所有必要的依赖包:")
    print("pip install -r requirements.txt")
    sys.exit(1)
except Exception as e:
    print(f"启动失败: {e}")
    sys.exit(1)
