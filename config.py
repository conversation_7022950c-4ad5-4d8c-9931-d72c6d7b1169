#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
多模态识别系统配置文件
"""

import os

# 系统配置
SYSTEM_CONFIG = {
    'name': '多模态识别系统',
    'version': '1.0.0',
    'author': 'Augment Agent',
    'description': '基于PyQt的多模态图像识别系统'
}

# 文件路径配置
PATHS = {
    'data_dir': 'data',
    'output_dir': 'output_images',
    'process_data_dir': 'process_data',
    'model_dir': 'model',
    'test_images_dir': 'test_images'
}

# 支持的文件格式
SUPPORTED_FORMATS = {
    'images': ['.png', '.jpg', '.jpeg', '.bmp', '.tif', '.tiff'],
    'videos': ['.mp4', '.avi', '.mov', '.mkv']
}

# YOLO模型配置
YOLO_CONFIG = {
    'default_model': 'yolo11n.pt',
    'models': {
        'visible_light': 'yolo11n.pt',
        'thermal_infrared': 'yolo11n.pt',  # 实际应用中应使用专门的红外模型
        'sar': 'yolo11n.pt'  # 实际应用中应使用专门的SAR模型
    },
    'confidence_threshold': 0.25,
    'iou_threshold': 0.45
}

# 特征提取配置
FEATURE_CONFIG = {
    'spatial': {
        'canny_low_threshold': 50,
        'canny_high_threshold': 150
    },
    'frequency': {
        'low_freq_ratio': 0.125,  # 中心1/8区域为低频
        'high_freq_ratio': 0.25   # 边缘1/4区域为高频
    },
    'wavelet': {
        'wavelet_type': 'db4',
        'levels': 1
    }
}

# 场景识别配置
SCENE_CONFIG = {
    'day_threshold': {
        'min_brightness': 100,
        'min_contrast': 20
    },
    'night_threshold': {
        'max_brightness': 80
    },
    'confidence_threshold': 0.5
}

# 探测体制权重配置
DETECTION_WEIGHTS = {
    'day': {
        'visible_light': 1.2,
        'thermal_infrared': 0.8,
        'sar': 1.0
    },
    'night': {
        'visible_light': 0.6,
        'thermal_infrared': 1.3,
        'sar': 1.0
    },
    'unknown': {
        'visible_light': 1.0,
        'thermal_infrared': 1.0,
        'sar': 1.0
    }
}

# 界面配置
UI_CONFIG = {
    'window_size': (1400, 900),
    'font_family': 'Microsoft YaHei',
    'font_size': 9,
    'image_display_size': (400, 300),
    'progress_update_interval': 100  # 毫秒
}

# 处理配置
PROCESSING_CONFIG = {
    'max_threads': 4,
    'auto_save_interval': 5,  # 秒
    'log_level': 'INFO',
    'enable_real_time_save': True
}

# 可视化配置
VISUALIZATION_CONFIG = {
    'figure_size': (12, 8),
    'dpi': 100,
    'color_map': 'hot',
    'bbox_colors': {
        'before_adjustment': (0, 0, 255),  # 红色
        'after_adjustment': (0, 255, 0)    # 绿色
    }
}

# 数据类型检测关键词
DATA_TYPE_KEYWORDS = {
    'visible_light': ['visible', 'rgb', 'color'],
    'thermal_infrared': ['thermal', 'infrared', 'ir'],
    'sar': ['sar', 'radar']
}

# 梯度特征配置
GRADIENT_CONFIG = {
    'sobel_kernel_size': 3,
    'direction_bins': 8,
    'magnitude_threshold': 10
}

def get_config(section: str = None):
    """获取配置"""
    if section is None:
        return {
            'system': SYSTEM_CONFIG,
            'paths': PATHS,
            'formats': SUPPORTED_FORMATS,
            'yolo': YOLO_CONFIG,
            'features': FEATURE_CONFIG,
            'scene': SCENE_CONFIG,
            'weights': DETECTION_WEIGHTS,
            'ui': UI_CONFIG,
            'processing': PROCESSING_CONFIG,
            'visualization': VISUALIZATION_CONFIG,
            'keywords': DATA_TYPE_KEYWORDS,
            'gradient': GRADIENT_CONFIG
        }
    
    config_map = {
        'system': SYSTEM_CONFIG,
        'paths': PATHS,
        'formats': SUPPORTED_FORMATS,
        'yolo': YOLO_CONFIG,
        'features': FEATURE_CONFIG,
        'scene': SCENE_CONFIG,
        'weights': DETECTION_WEIGHTS,
        'ui': UI_CONFIG,
        'processing': PROCESSING_CONFIG,
        'visualization': VISUALIZATION_CONFIG,
        'keywords': DATA_TYPE_KEYWORDS,
        'gradient': GRADIENT_CONFIG
    }
    
    return config_map.get(section, {})

def create_directories():
    """创建必要的目录"""
    for path in PATHS.values():
        os.makedirs(path, exist_ok=True)
    print("目录结构已创建")

if __name__ == "__main__":
    # 创建目录结构
    create_directories()
    
    # 显示配置信息
    config = get_config()
    print("多模态识别系统配置:")
    print(f"系统名称: {config['system']['name']}")
    print(f"版本: {config['system']['version']}")
    print(f"支持的图像格式: {config['formats']['images']}")
    print(f"支持的视频格式: {config['formats']['videos']}")
    print(f"默认YOLO模型: {config['yolo']['default_model']}")
    print("配置加载完成！")
