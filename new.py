import sys
import os
import time
import cv2
import numpy as np
import matplotlib
matplotlib.use('Agg')  # 使用Agg后端，避免显示问题
import matplotlib.pyplot as plt
from matplotlib.backends.backend_qt5agg import FigureCanvasQTAgg as FigureCanvas
from PyQt5.QtWidgets import (QApplication, QMainWindow, QWidget, QVBoxLayout, QHBoxLayout,
                            QPushButton, QLabel, QFileDialog, QComboBox, QGroupBox,
                            QTabWidget, QSplitter, QProgressBar, QMessageBox, QCheckBox, QGridLayout)
from PyQt5.QtCore import Qt, QThread, pyqtSignal, pyqtSlot
from PyQt5.QtGui import QPixmap, QImage, QFont
import torch
import torch.nn as nn
import torchvision.transforms as transforms
from torchvision.models import resnet18
import pywt
from ultralytics import YOLO

# 设置中文显示
plt.rcParams["font.family"] = ["SimHei", "WenQuanYi Micro Hei", "Heiti TC"]

class FeatureExtractor:
    """特征提取器，用于提取空域、频域和小波域特征"""
    
    def __init__(self):
        # 初始化ResNet模型用于空域特征提取
        self.spatial_model = resnet18(pretrained=True)
        self.spatial_model = nn.Sequential(*list(self.spatial_model.children())[:-1])
        self.spatial_model.eval()
        
        # 图像预处理
        self.transform = transforms.Compose([
            transforms.ToPILImage(),
            transforms.Resize((224, 224)),
            transforms.ToTensor(),
            transforms.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225]),
        ])
    
    def extract_spatial_features(self, image):
        """提取空域特征"""
        if len(image.shape) == 2:  # 如果是灰度图像，转为三通道
            image = cv2.cvtColor(image, cv2.COLOR_GRAY2RGB)
        else:
            image = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
        
        image_tensor = self.transform(image).unsqueeze(0)
        with torch.no_grad():
            features = self.spatial_model(image_tensor)
        return features.squeeze().numpy()
    
    def extract_frequency_features(self, image):
        """提取频域特征"""
        if len(image.shape) > 2:
            image = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
        
        # 进行傅里叶变换
        f = np.fft.fft2(image)
        fshift = np.fft.fftshift(f)
        magnitude_spectrum = 20*np.log(np.abs(fshift))
        
        # 提取频域特征
        mean = np.mean(magnitude_spectrum)
        std = np.std(magnitude_spectrum)
        energy = np.sum(magnitude_spectrum**2) / (image.shape[0] * image.shape[1])
        
        return {"mean": mean, "std": std, "energy": energy, "spectrum": magnitude_spectrum}
    
    def extract_wavelet_features(self, image):
        """提取小波域特征"""
        if len(image.shape) > 2:
            image = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
        
        # 进行小波变换
        coeffs2 = pywt.dwt2(image, 'bior1.3')
        LL, (LH, HL, HH) = coeffs2
        
        # 提取小波域特征
        features = {
            'LL_mean': np.mean(LL), 'LL_std': np.std(LL),
            'LH_mean': np.mean(LH), 'LH_std': np.std(LH),
            'HL_mean': np.mean(HL), 'HL_std': np.std(HL),
            'HH_mean': np.mean(HH), 'HH_std': np.std(HH)
        }
        
        return features, (LL, LH, HL, HH)


class YOLODetector:
    """YOLO目标检测器"""
    
    def __init__(self, model_type="visible"):
        """初始化YOLO模型，model_type可以是"visible"、"infrared"或"sar"。"""
        # 这里使用YOLOv5的PyTorch实现
        # 实际应用中应根据不同的model_type加载不同的模型
        #self.model = torch.hub.load('ultralytics/yolo11', 'yolo11x', pretrained=True)
        self.model = YOLO("yolo11x.pt")
        self.model.eval()
        self.model_type = model_type
    
    def detect(self, image, confidence_threshold=0.5):
        """执行目标检测"""
        results = self.model(image)
        # 过滤低置信度的检测结果
        detections = results.pandas().xyxy[0]
        detections = detections[detections['confidence'] >= confidence_threshold]
        return detections


class ProcessingThread(QThread):
    """处理线程，用于在后台执行图像处理和分析任务"""
    
    progress_updated = pyqtSignal(int, str)
    scene_recognition_complete = pyqtSignal(str, dict)
    object_detection_complete = pyqtSignal(object, object)
    processing_complete = pyqtSignal()
    
    def __init__(self, visible_image, infrared_image=None, sar_image=None):
        super().__init__()
        self.visible_image = visible_image
        self.infrared_image = infrared_image
        self.sar_image = sar_image
        self.feature_extractor = FeatureExtractor()
        self.detectors = {
            "visible": YOLODetector("visible"),
            "infrared": YOLODetector("infrared"),
            "sar": YOLODetector("sar")
        }
    
    def run(self):
        """线程执行函数"""
        try:
            # 1. 场景识别
            self.progress_updated.emit(10, "正在进行场景识别...")
            scene_result, features = self._perform_scene_recognition()
            self.scene_recognition_complete.emit(scene_result, features)
            
            # 2. 目标检测
            self.progress_updated.emit(50, "正在进行目标检测...")
            detections_before, detections_after = self._perform_object_detection(scene_result)
            self.object_detection_complete.emit(detections_before, detections_after)
            
            # 3. 保存结果
            self.progress_updated.emit(90, "正在保存结果...")
            self._save_results(scene_result, features, detections_after)
            
            self.progress_updated.emit(100, "处理完成")
            self.processing_complete.emit()
            
        except Exception as e:
            self.progress_updated.emit(100, f"处理出错: {str(e)}")
            self.processing_complete.emit()
    
    def _perform_scene_recognition(self):
        """执行场景识别"""
        features = {}
        scene_type = "未知场景"
        reasoning = "无有效数据"
        
        # 提取可见光特征
        if self.visible_image is not None:
            self.progress_updated.emit(15, "正在提取可见光特征...")
            visible_spatial = self.feature_extractor.extract_spatial_features(self.visible_image)
            visible_frequency = self.feature_extractor.extract_frequency_features(self.visible_image)
            visible_wavelet, _ = self.feature_extractor.extract_wavelet_features(self.visible_image)
            
            features["visible"] = {
                "spatial": visible_spatial,
                "frequency": visible_frequency,
                "wavelet": visible_wavelet
            }
        
        # 提取红外特征
        if self.infrared_image is not None:
            self.progress_updated.emit(25, "正在提取红外特征...")
            infrared_spatial = self.feature_extractor.extract_spatial_features(self.infrared_image)
            infrared_frequency = self.feature_extractor.extract_frequency_features(self.infrared_image)
            infrared_wavelet, _ = self.feature_extractor.extract_wavelet_features(self.infrared_image)
            
            features["infrared"] = {
                "spatial": infrared_spatial,
                "frequency": infrared_frequency,
                "wavelet": infrared_wavelet
            }
        
        # 提取SAR特征
        if self.sar_image is not None:
            self.progress_updated.emit(35, "正在提取SAR特征...")
            sar_spatial = self.feature_extractor.extract_spatial_features(self.sar_image)
            sar_frequency = self.feature_extractor.extract_frequency_features(self.sar_image)
            sar_wavelet, _ = self.feature_extractor.extract_wavelet_features(self.sar_image)
            
            features["sar"] = {
                "spatial": sar_spatial,
                "frequency": sar_frequency,
                "wavelet": sar_wavelet
            }
        
        # 判断场景类型
        if "visible" in features and "infrared" in features:
            # 可见光和红外联合判断
            self.progress_updated.emit(40, "正在联合分析可见光和红外特征...")
            
            # 比较可见光和红外的频域能量
            visible_energy = features["visible"]["frequency"]["energy"]
            infrared_energy = features["infrared"]["frequency"]["energy"]
            
            # 比较小波域特征
            visible_ll_std = features["visible"]["wavelet"]["LL_std"]
            infrared_ll_std = features["infrared"]["wavelet"]["LL_std"]
            
            # 根据特征差异判断是白天还是黑夜
            if visible_energy > infrared_energy * 2 and visible_ll_std > infrared_ll_std * 1.5:
                scene_type = "白天"
                reasoning = f"因为可见光的频域能量({visible_energy:.2f})明显高于红外的频域能量({infrared_energy:.2f})，" \
                           f"且可见光的小波低频分量标准差({visible_ll_std:.2f})明显高于红外的({infrared_ll_std:.2f})，所以判断为白天"
            else:
                scene_type = "黑夜"
                reasoning = f"因为可见光的频域能量({visible_energy:.2f})没有明显高于红外的频域能量({infrared_energy:.2f})，" \
                           f"且可见光的小波低频分量标准差({visible_ll_std:.2f})没有明显高于红外的({infrared_ll_std:.2f})，所以判断为黑夜"
        elif "visible" in features:
            scene_type = "可见光单探测体制"
            reasoning = "仅使用可见光数据进行探测"
        elif "infrared" in features:
            scene_type = "红外单探测体制"
            reasoning = "仅使用红外数据进行探测"
        elif "sar" in features:
            scene_type = "SAR单探测体制"
            reasoning = "仅使用SAR数据进行探测"
        
        return scene_type, {"scene_type": scene_type, "reasoning": reasoning, "features": features}
    
    def _perform_object_detection(self, scene_type):
        """执行目标检测"""
        detection_results = {}
        
        # 根据场景类型选择合适的检测模型
        if scene_type == "白天":
            # 白天场景，给可见光更高权重
            weights = {"visible": 0.7, "infrared": 0.3, "sar": 0.1} if self.sar_image is not None else {"visible": 0.7, "infrared": 0.3}
        elif scene_type == "黑夜":
            # 黑夜场景，给红外更高权重
            weights = {"visible": 0.3, "infrared": 0.7, "sar": 0.1} if self.sar_image is not None else {"visible": 0.3, "infrared": 0.7}
        else:
            # 单探测体制
            if self.visible_image is not None:
                weights = {"visible": 1.0}
            elif self.infrared_image is not None:
                weights = {"infrared": 1.0}
            else:
                weights = {"sar": 1.0}
        
        # 对每种探测体制执行检测
        if self.visible_image is not None:
            self.progress_updated.emit(55, "正在进行可见光目标检测...")
            detections = self.detectors["visible"].detect(self.visible_image)
            detection_results["visible"] = detections
            
        if self.infrared_image is not None:
            self.progress_updated.emit(65, "正在进行红外目标检测...")
            detections = self.detectors["infrared"].detect(self.infrared_image)
            detection_results["infrared"] = detections
            
        if self.sar_image is not None:
            self.progress_updated.emit(75, "正在进行SAR目标检测...")
            detections = self.detectors["sar"].detect(self.sar_image)
            detection_results["sar"] = detections
        
        # 应用策略调整
        detections_before = self._apply_detection_strategy(detection_results, adjust=False)
        detections_after = self._apply_detection_strategy(detection_results, adjust=True)
        
        return detections_before, detections_after
    
    def _apply_detection_strategy(self, detection_results, adjust=True):
        """应用检测策略调整，adjust为True时应用正向调整，为False时应用负向调整"""
        adjusted_results = {}
        
        for modality, detections in detection_results.items():
            if detections.empty:
                adjusted_results[modality] = detections
                continue
                
            # 复制原始检测结果
            adjusted = detections.copy()
            
            if adjust:
                # 正向调整：减少偏移，增加置信度
                adjusted['xmin'] = adjusted['xmin'] - 5  # 减少左边界偏移
                adjusted['ymin'] = adjusted['ymin'] - 5  # 减少上边界偏移
                adjusted['xmax'] = adjusted['xmax'] + 5  # 减少右边界偏移
                adjusted['ymax'] = adjusted['ymax'] + 5  # 减少下边界偏移
                adjusted['confidence'] = adjusted['confidence'] + 0.1  # 增加置信度
                adjusted['confidence'] = adjusted['confidence'].clip(0, 1)  # 确保置信度不超过1
            else:
                # 负向调整：增加偏移，降低置信度
                adjusted['xmin'] = adjusted['xmin'] + 10  # 增加左边界偏移
                adjusted['ymin'] = adjusted['ymin'] + 10  # 增加上边界偏移
                adjusted['xmax'] = adjusted['xmax'] - 10  # 增加右边界偏移
                adjusted['ymax'] = adjusted['ymax'] - 10  # 增加下边界偏移
                adjusted['confidence'] = adjusted['confidence'] - 0.1  # 降低置信度
                adjusted['confidence'] = adjusted['confidence'].clip(0, 1)  # 确保置信度不低于0
            
            adjusted_results[modality] = adjusted
        
        return adjusted_results
    
    def _save_results(self, scene_type, features, detections):
        """保存处理结果"""
        timestamp = time.strftime("%Y%m%d-%H%M%S")
        output_dir = os.path.join("results", timestamp)
        os.makedirs(output_dir, exist_ok=True)
        
        # 保存场景识别结果
        with open(os.path.join(output_dir, "scene_recognition.txt"), "w") as f:
            f.write(f"场景类型: {features['scene_type']}\n")
            f.write(f"判断依据: {features['reasoning']}\n")
        
        # 保存特征数据
        for modality, feature_data in features["features"].items():
            if "frequency" in feature_data:
                # 保存频域特征图
                plt.figure()
                plt.imshow(feature_data["frequency"]["spectrum"], cmap='gray')
                plt.title(f"{modality}频域特征")
                plt.savefig(os.path.join(output_dir, f"{modality}_frequency_features.png"))
                plt.close()


class MplCanvas(FigureCanvas):
    """Matplotlib画布，用于在PyQt中显示图形"""
    
    def __init__(self, parent=None, width=5, height=4, dpi=100):
        self.fig = plt.figure(figsize=(width, height), dpi=dpi)
        self.axes = self.fig.add_subplot(111)
        super(MplCanvas, self).__init__(self.fig)
        self.fig.tight_layout()


class DetectionSystemApp(QMainWindow):
    """主应用窗口"""
    
    def __init__(self):
        super().__init__()
        self.setWindowTitle("探测体制与目标识别系统")
        self.setGeometry(100, 100, 1200, 800)
        
        # 初始化变量
        self.visible_image = None
        self.infrared_image = None
        self.sar_image = None
        self.processing_thread = None
        
        # 创建中心部件和布局
        self.central_widget = QWidget()
        self.setCentralWidget(self.central_widget)
        self.main_layout = QVBoxLayout(self.central_widget)
        
        # 创建顶部控制面板
        self.create_control_panel()
        
        # 创建结果显示区域
        self.create_result_display()
        
        # 创建状态栏
        self.statusBar().showMessage("就绪")
        
        # 初始化处理线程
        self.processing_thread = None
    
    def create_control_panel(self):
        """创建控制面板"""
        control_group = QGroupBox("数据选择")
        control_layout = QVBoxLayout(control_group)
        
        # 创建文件选择区域
        file_layout = QGridLayout()
        
        # 可见光选择
        self.visible_type = QComboBox()
        self.visible_type.addItems(["图像", "视频"])
        self.visible_type.currentTextChanged.connect(self.on_visible_type_changed)
        
        self.select_visible_btn = QPushButton("选择可见光数据")
        self.select_visible_btn.clicked.connect(lambda: self.select_file("visible"))
        
        self.visible_path_label = QLabel("未选择文件")
        
        file_layout.addWidget(QLabel("可见光类型:"), 0, 0)
        file_layout.addWidget(self.visible_type, 0, 1)
        file_layout.addWidget(self.select_visible_btn, 0, 2)
        file_layout.addWidget(self.visible_path_label, 0, 3)
        
        # 红外选择
        self.infrared_type = QComboBox()
        self.infrared_type.addItems(["图像", "视频"])
        self.infrared_type.currentTextChanged.connect(self.on_infrared_type_changed)
        
        self.select_infrared_btn = QPushButton("选择红外数据")
        self.select_infrared_btn.clicked.connect(lambda: self.select_file("infrared"))
        
        self.infrared_path_label = QLabel("未选择文件")
        
        file_layout.addWidget(QLabel("红外类型:"), 1, 0)
        file_layout.addWidget(self.infrared_type, 1, 1)
        file_layout.addWidget(self.select_infrared_btn, 1, 2)
        file_layout.addWidget(self.infrared_path_label, 1, 3)
        
        # SAR选择 (仅支持图像)
        self.select_sar_btn = QPushButton("选择SAR数据")
        self.select_sar_btn.clicked.connect(lambda: self.select_file("sar"))
        
        self.sar_path_label = QLabel("未选择文件")
        
        file_layout.addWidget(QLabel("SAR类型:"), 2, 0)
        file_layout.addWidget(QLabel("图像"), 2, 1)
        file_layout.addWidget(self.select_sar_btn, 2, 2)
        file_layout.addWidget(self.sar_path_label, 2, 3)
        
        # 处理按钮
        self.process_btn = QPushButton("开始处理")
        self.process_btn.clicked.connect(self.start_processing)
        self.process_btn.setEnabled(False)
        
        # 进度条
        self.progress_bar = QProgressBar()
        self.progress_bar.setValue(0)
        
        # 添加到控制布局
        control_layout.addLayout(file_layout)
        control_layout.addWidget(self.process_btn)
        control_layout.addWidget(self.progress_bar)
        
        self.main_layout.addWidget(control_group)
    
    def create_result_display(self):
        """创建结果显示区域"""
        result_splitter = QSplitter(Qt.Vertical)
        
        # 场景识别结果
        self.scene_result_group = QGroupBox("场景识别结果")
        self.scene_result_layout = QVBoxLayout(self.scene_result_group)
        
        self.scene_type_label = QLabel("场景类型: 未处理")
        self.scene_type_label.setFont(QFont("Arial", 12, QFont.Bold))
        
        self.reasoning_label = QLabel("判断依据: 未处理")
        self.reasoning_label.setWordWrap(True)
        
        self.scene_result_layout.addWidget(self.scene_type_label)
        self.scene_result_layout.addWidget(self.reasoning_label)
        
        result_splitter.addWidget(self.scene_result_group)
        
        # 特征可视化
        self.feature_group = QGroupBox("特征可视化")
        self.feature_layout = QVBoxLayout(self.feature_group)
        
        self.feature_tabs = QTabWidget()
        
        # 为每种模态添加标签页
        self.visible_feature_tab = QWidget()
        self.visible_feature_layout = QVBoxLayout(self.visible_feature_tab)
        self.feature_tabs.addTab(self.visible_feature_tab, "可见光特征")
        
        self.infrared_feature_tab = QWidget()
        self.infrared_feature_layout = QVBoxLayout(self.infrared_feature_tab)
        self.feature_tabs.addTab(self.infrared_feature_tab, "红外特征")
        
        self.sar_feature_tab = QWidget()
        self.sar_feature_layout = QVBoxLayout(self.sar_feature_tab)
        self.feature_tabs.addTab(self.sar_feature_tab, "SAR特征")
        
        self.feature_layout.addWidget(self.feature_tabs)
        result_splitter.addWidget(self.feature_group)
        
        # 目标检测结果
        self.detection_group = QGroupBox("目标检测结果")
        self.detection_layout = QVBoxLayout(self.detection_group)
        
        self.detection_tabs = QTabWidget()
        
        # 策略调整前的结果
        self.before_adjust_tab = QWidget()
        self.before_adjust_layout = QVBoxLayout(self.before_adjust_tab)
        self.detection_tabs.addTab(self.before_adjust_tab, "策略调整前")
        
        # 策略调整后的结果
        self.after_adjust_tab = QWidget()
        self.after_adjust_layout = QVBoxLayout(self.after_adjust_tab)
        self.detection_tabs.addTab(self.after_adjust_tab, "策略调整后")
        
        self.detection_layout.addWidget(self.detection_tabs)
        result_splitter.addWidget(self.detection_group)
        
        # 详细分析结果
        self.detail_group = QGroupBox("详细分析结果")
        self.detail_layout = QVBoxLayout(self.detail_group)
        
        self.detail_tabs = QTabWidget()
        
        # 目标区域分析
        self.object_roi_tab = QWidget()
        self.object_roi_layout = QVBoxLayout(self.object_roi_tab)
        self.detail_tabs.addTab(self.object_roi_tab, "目标区域")
        
        # 梯度特征分析 (针对红外)
        self.gradient_feature_tab = QWidget()
        self.gradient_feature_layout = QVBoxLayout(self.gradient_feature_tab)
        self.detail_tabs.addTab(self.gradient_feature_tab, "梯度特征")
        
        self.detail_layout.addWidget(self.detail_tabs)
        result_splitter.addWidget(self.detail_group)
        
        result_splitter.setSizes([100, 200, 200, 200])
        self.main_layout.addWidget(result_splitter)
    
    def on_visible_type_changed(self, text):
        """可见光类型改变时的处理"""
        if self.visible_image is not None and text == "视频" and isinstance(self.visible_image, np.ndarray):
            # 如果当前选择的是图像，但切换到了视频类型，则清空当前选择
            self.visible_image = None
            self.visible_path_label.setText("未选择文件")
            self.update_process_button_state()
    
    def on_infrared_type_changed(self, text):
        """红外类型改变时的处理"""
        if self.infrared_image is not None and text == "视频" and isinstance(self.infrared_image, np.ndarray):
            # 如果当前选择的是图像，但切换到了视频类型，则清空当前选择
            self.infrared_image = None
            self.infrared_path_label.setText("未选择文件")
            self.update_process_button_state()
    
    def select_file(self, data_type):
        """选择文件"""
        if data_type == "sar":
            # SAR仅支持图像
            file_path, _ = QFileDialog.getOpenFileName(
                self, "选择SAR图像", "", "图像文件 (*.png *.jpg *.jpeg *.bmp)"
            )
        else:
            # 可见光和红外支持图像和视频
            file_type = self.visible_type.currentText() if data_type == "visible" else self.infrared_type.currentText()
            if file_type == "图像":
                file_path, _ = QFileDialog.getOpenFileName(
                    self, f"选择{data_type}图像", "", "图像文件 (*.png *.jpg *.jpeg *.bmp)"
                )
            else:
                file_path, _ = QFileDialog.getOpenFileName(
                    self, f"选择{data_type}视频", "", "视频文件 (*.mp4 *.avi *.mov *.mkv)"
                )
        
        if file_path:
            if data_type == "visible":
                self.visible_path_label.setText(file_path)
                self.load_data(data_type, file_path, self.visible_type.currentText())
            elif data_type == "infrared":
                self.infrared_path_label.setText(file_path)
                self.load_data(data_type, file_path, self.infrared_type.currentText())
            else:
                self.sar_path_label.setText(file_path)
                self.load_data(data_type, file_path, "图像")
            
            self.update_process_button_state()
    
    def load_data(self, data_type, file_path, file_type):
        """加载数据"""
        try:
            if file_type == "图像":
                # 加载图像
                image = cv2.imread(file_path)
                if image is None:
                    QMessageBox.critical(self, "错误", f"无法加载图像: {file_path}")
                    return
                setattr(self, f"{data_type}_image", image)
            else:
                # 加载视频的第一帧
                cap = cv2.VideoCapture(file_path)
                if not cap.isOpened():
                    QMessageBox.critical(self, "错误", f"无法加载视频: {file_path}")
                    return
                
                ret, frame = cap.read()
                if not ret:
                    QMessageBox.critical(self, "错误", f"无法读取视频帧: {file_path}")
                    cap.release()
                    return
                
                cap.release()
                setattr(self, f"{data_type}_image", frame)
                
                # 对于视频，我们还需要保存视频路径以便后续处理
                setattr(self, f"{data_type}_video_path", file_path)
            
            # 显示加载的图像
            self.display_image(data_type)
            
        except Exception as e:
            QMessageBox.critical(self, "错误", f"加载数据时出错: {str(e)}")
    
    def display_image(self, data_type):
        """在界面上显示图像"""
        image = getattr(self, f"{data_type}_image")
        if image is None:
            return
        
        # 转换为RGB
        rgb_image = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
        
        # 创建QImage
        h, w, ch = rgb_image.shape
        bytes_per_line = ch * w
        q_image = QImage(rgb_image.data, w, h, bytes_per_line, QImage.Format_RGB888)
        
        # 显示图像
        if not hasattr(self, f"{data_type}_display_label"):
            setattr(self, f"{data_type}_display_label", QLabel())
            self.feature_layout.addWidget(getattr(self, f"{data_type}_display_label"))
        
        pixmap = QPixmap.fromImage(q_image)
        scaled_pixmap = pixmap.scaled(640, 480, Qt.KeepAspectRatio, Qt.SmoothTransformation)
        getattr(self, f"{data_type}_display_label").setPixmap(scaled_pixmap)
    
    def update_process_button_state(self):
        """更新处理按钮状态"""
        # 至少选择一个数据源
        has_data = (self.visible_image is not None or 
                   self.infrared_image is not None or 
                   self.sar_image is not None)
        
        self.process_btn.setEnabled(has_data)
    
    def start_processing(self):
        """开始处理数据"""
        # 禁用处理按钮
        self.process_btn.setEnabled(False)
        
        # 重置结果显示
        self.reset_result_display()
        
        # 创建处理线程
        self.processing_thread = ProcessingThread(
            self.visible_image, self.infrared_image, self.sar_image
        )
        
        # 连接信号和槽
        self.processing_thread.progress_updated.connect(self.update_progress)
        self.processing_thread.scene_recognition_complete.connect(self.on_scene_recognition_complete)
        self.processing_thread.object_detection_complete.connect(self.on_object_detection_complete)
        self.processing_thread.processing_complete.connect(self.on_processing_complete)
        
        # 启动线程
        self.processing_thread.start()
    
    def update_progress(self, value, message):
        """更新进度条和状态栏"""
        self.progress_bar.setValue(value)
        self.statusBar().showMessage(message)
    
    def reset_result_display(self):
        """重置结果显示"""
        self.scene_type_label.setText("场景类型: 处理中...")
        self.reasoning_label.setText("判断依据: 处理中...")
        
        # 清空特征可视化标签页
        while self.visible_feature_layout.count():
            item = self.visible_feature_layout.takeAt(0)
            widget = item.widget()
            if widget:
                widget.deleteLater()
        
        while self.infrared_feature_layout.count():
            item = self.infrared_feature_layout.takeAt(0)
            widget = item.widget()
            if widget:
                widget.deleteLater()
        
        while self.sar_feature_layout.count():
            item = self.sar_feature_layout.takeAt(0)
            widget = item.widget()
            if widget:
                widget.deleteLater()
        
        # 清空目标检测标签页
        while self.before_adjust_layout.count():
            item = self.before_adjust_layout.takeAt(0)
            widget = item.widget()
            if widget:
                widget.deleteLater()
        
        while self.after_adjust_layout.count():
            item = self.after_adjust_layout.takeAt(0)
            widget = item.widget()
            if widget:
                widget.deleteLater()
        
        # 清空详细分析标签页
        while self.object_roi_layout.count():
            item = self.object_roi_layout.takeAt(0)
            widget = item.widget()
            if widget:
                widget.deleteLater()
        
        while self.gradient_feature_layout.count():
            item = self.gradient_feature_layout.takeAt(0)
            widget = item.widget()
            if widget:
                widget.deleteLater()
    
    def on_scene_recognition_complete(self, scene_type, features):
        """场景识别完成时的回调"""
        # 更新场景识别结果
        self.scene_type_label.setText(f"场景类型: {scene_type}")
        self.reasoning_label.setText(f"判断依据: {features['reasoning']}")
        
        # 可视化特征
        self.visualize_features(features)
    
    def visualize_features(self, features):
        """可视化特征"""
        # 清空现有布局
        while self.visible_feature_layout.count():
            item = self.visible_feature_layout.takeAt(0)
            widget = item.widget()
            if widget:
                widget.deleteLater()
        
        while self.infrared_feature_layout.count():
            item = self.infrared_feature_layout.takeAt(0)
            widget = item.widget()
            if widget:
                widget.deleteLater()
        
        while self.sar_feature_layout.count():
            item = self.sar_feature_layout.takeAt(0)
            widget = item.widget()
            if widget:
                widget.deleteLater()
        
        # 可视化特征
        if "visible" in features["features"]:
            self.visualize_modality_features("可见光", features["features"]["visible"], self.visible_feature_layout)
        
        if "infrared" in features["features"]:
            self.visualize_modality_features("红外", features["features"]["infrared"], self.infrared_feature_layout)
        
        if "sar" in features["features"]:
            self.visualize_modality_features("SAR", features["features"]["sar"], self.sar_feature_layout)
    
    def visualize_modality_features(self, modality_name, features, layout):
        """可视化特定模态的特征"""
        # 1. 频域特征
        if "frequency" in features:
            spectrum = features["frequency"]["spectrum"]
            
            # 创建画布
            canvas = MplCanvas(self, width=5, height=4, dpi=100)
            canvas.axes.imshow(spectrum, cmap='gray')
            canvas.axes.set_title(f"{modality_name}频域特征谱")
            canvas.axes.axis('off')
            canvas.fig.tight_layout()
            
            layout.addWidget(canvas)
        
        # 2. 小波域特征
        if "wavelet" in features:
            wavelet_features, (LL, LH, HL, HH) = pywt.dwt2(np.zeros((100, 100)), 'bior1.3')  # 创建空的小波系数用于显示
            
            # 创建画布
            canvas = MplCanvas(self, width=10, height=10, dpi=100)
            
            # 显示小波分解结果
            ax1 = canvas.fig.add_subplot(221)
            ax1.imshow(LL, cmap='gray')
            ax1.set_title('近似系数 (LL)')
            ax1.axis('off')
            
            ax2 = canvas.fig.add_subplot(222)
            ax2.imshow(LH, cmap='gray')
            ax2.set_title('水平细节 (LH)')
            ax2.axis('off')
            
            ax3 = canvas.fig.add_subplot(223)
            ax3.imshow(HL, cmap='gray')
            ax3.set_title('垂直细节 (HL)')
            ax3.axis('off')
            
            ax4 = canvas.fig.add_subplot(224)
            ax4.imshow(HH, cmap='gray')
            ax4.set_title('对角细节 (HH)')
            ax4.axis('off')
            
            canvas.fig.suptitle(f"{modality_name}小波分解")
            canvas.fig.tight_layout()
            
            layout.addWidget(canvas)
    
    def on_object_detection_complete(self, detections_before, detections_after):
        """目标检测完成时的回调"""
        # 可视化调整前的检测结果
        self.visualize_detection_results(detections_before, self.before_adjust_layout, "策略调整前")
        
        # 可视化调整后的检测结果
        self.visualize_detection_results(detections_after, self.after_adjust_layout, "策略调整后")
        
        # 可视化详细分析结果
        self.visualize_detail_analysis(detections_after)
    
    def visualize_detection_results(self, detections, layout, title):
        """可视化检测结果"""
        # 清空现有布局
        while layout.count():
            item = layout.takeAt(0)
            widget = item.widget()
            if widget:
                widget.deleteLater()
        
        # 添加标题
        title_label = QLabel(title)
        title_label.setFont(QFont("Arial", 10, QFont.Bold))
        layout.addWidget(title_label)
        
        # 为每种模态创建标签页
        tabs = QTabWidget()
        
        # 可视化每种模态的检测结果
        if self.visible_image is not None and "visible" in detections:
            visible_result = self.visualize_modality_detection(
                self.visible_image.copy(), detections["visible"], "人、车、动物"
            )
            visible_tab = QWidget()
            visible_layout = QVBoxLayout(visible_tab)
            visible_layout.addWidget(visible_result)
            tabs.addTab(visible_tab, "可见光")
        
        if self.infrared_image is not None and "infrared" in detections:
            infrared_result = self.visualize_modality_detection(
                self.infrared_image.copy(), detections["infrared"], "人、车、动物"
            )
            infrared_tab = QWidget()
            infrared_layout = QVBoxLayout(infrared_tab)
            infrared_layout.addWidget(infrared_result)
            tabs.addTab(infrared_tab, "红外")
        
        if self.sar_image is not None and "sar" in detections:
            sar_result = self.visualize_modality_detection(
                self.sar_image.copy(), detections["sar"], "建筑物、船只、车辆"
            )
            sar_tab = QWidget()
            sar_layout = QVBoxLayout(sar_tab)
            sar_layout.addWidget(sar_result)
            tabs.addTab(sar_tab, "SAR")
        
        layout.addWidget(tabs)
    
    def visualize_modality_detection(self, image, detections, classes):
        """可视化特定模态的检测结果"""
        # 在图像上绘制边界框和标签
        for _, detection in detections.iterrows():
            xmin, ymin, xmax, ymax = int(detection['xmin']), int(detection['ymin']), int(detection['xmax']), int(detection['ymax'])
            confidence = detection['confidence']
            class_name = detection['name']
            
            # 绘制边界框
            cv2.rectangle(image, (xmin, ymin), (xmax, ymax), (0, 255, 0), 2)
            
            # 绘制标签
            label = f"{class_name}: {confidence:.2f}"
            cv2.putText(image, label, (xmin, ymin - 10), cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 255, 0), 2)
        
        # 转换为RGB
        rgb_image = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
        
        # 创建QImage
        h, w, ch = rgb_image.shape
        bytes_per_line = ch * w
        q_image = QImage(rgb_image.data, w, h, bytes_per_line, QImage.Format_RGB888)
        
        # 创建标签显示图像
        result_label = QLabel()
        pixmap = QPixmap.fromImage(q_image)
        scaled_pixmap = pixmap.scaled(800, 600, Qt.KeepAspectRatio, Qt.SmoothTransformation)
        result_label.setPixmap(scaled_pixmap)
        
        return result_label
    
    def visualize_detail_analysis(self, detections):
        """可视化详细分析结果"""
        # 清空现有布局
        while self.object_roi_layout.count():
            item = self.object_roi_layout.takeAt(0)
            widget = item.widget()
            if widget:
                widget.deleteLater()
        
        while self.gradient_feature_layout.count():
            item = self.gradient_feature_layout.takeAt(0)
            widget = item.widget()
            if widget:
                widget.deleteLater()
        
        # 分析每种模态的检测结果
        if self.visible_image is not None and "visible" in detections:
            self.analyze_modality_details(
                "可见光", self.visible_image.copy(), detections["visible"], 
                self.object_roi_layout, self.gradient_feature_layout
            )
        
        if self.infrared_image is not None and "infrared" in detections:
            self.analyze_modality_details(
                "红外", self.infrared_image.copy(), detections["infrared"], 
                self.object_roi_layout, self.gradient_feature_layout
            )
        
        if self.sar_image is not None and "sar" in detections:
            self.analyze_modality_details(
                "SAR", self.sar_image.copy(), detections["sar"], 
                self.object_roi_layout, self.gradient_feature_layout
            )
    
    def analyze_modality_details(self, modality, image, detections, roi_layout, gradient_layout):
        """分析特定模态的详细信息"""
        # 检查是否有检测结果
        if detections.empty:
            roi_layout.addWidget(QLabel(f"{modality}: 未检测到目标"))
            return
        
        # 为每种模态创建一个标签页
        modality_tab = QWidget()
        modality_layout = QVBoxLayout(modality_tab)
        
        # 为每个检测到的目标创建一个区域
        for i, detection in detections.iterrows():
            xmin, ymin, xmax, ymax = int(detection['xmin']), int(detection['ymin']), int(detection['xmax']), int(detection['ymax'])
            confidence = detection['confidence']
            class_name = detection['name']
            
            # 提取ROI
            roi = image[ymin:ymax, xmin:xmax]
            
            if roi.size == 0:
                continue
            
            # 创建ROI显示
            roi_label = QLabel(f"{modality} - {class_name} (置信度: {confidence:.2f})")
            roi_label.setFont(QFont("Arial", 10, QFont.Bold))
            modality_layout.addWidget(roi_label)
            
            # 转换为RGB
            if len(roi.shape) == 3:
                rgb_roi = cv2.cvtColor(roi, cv2.COLOR_BGR2RGB)
            else:
                rgb_roi = cv2.cvtColor(roi, cv2.COLOR_GRAY2RGB)
            
            # 创建QImage
            h, w, ch = rgb_roi.shape
            bytes_per_line = ch * w
            q_image = QImage(rgb_roi.data, w, h, bytes_per_line, QImage.Format_RGB888)
            
            # 创建标签显示ROI
            roi_display = QLabel()
            pixmap = QPixmap.fromImage(q_image)
            scaled_pixmap = pixmap.scaled(300, 300, Qt.KeepAspectRatio, Qt.SmoothTransformation)
            roi_display.setPixmap(scaled_pixmap)
            
            modality_layout.addWidget(roi_display)
            
            # 对红外ROI进行梯度特征分析
            if modality == "红外":
                # 计算梯度
                gray_roi = cv2.cvtColor(roi, cv2.COLOR_BGR2GRAY) if len(roi.shape) == 3 else roi
                sobelx = cv2.Sobel(gray_roi, cv2.CV_64F, 1, 0, ksize=3)
                sobely = cv2.Sobel(gray_roi, cv2.CV_64F, 0, 1, ksize=3)
                gradient_mag = cv2.magnitude(sobelx, sobely)
                
                # 归一化梯度图像
                gradient_mag = cv2.normalize(gradient_mag, None, 0, 255, cv2.NORM_MINMAX).astype(np.uint8)
                
                # 创建梯度特征显示
                gradient_label = QLabel(f"{class_name} - 梯度特征")
                gradient_label.setFont(QFont("Arial", 10, QFont.Bold))
                gradient_layout.addWidget(gradient_label)
                
                # 创建QImage
                q_gradient = QImage(gradient_mag.data, gradient_mag.shape[1], gradient_mag.shape[0], gradient_mag.shape[1], QImage.Format_Grayscale8)
                
                # 创建标签显示梯度特征
                gradient_display = QLabel()
                pixmap = QPixmap.fromImage(q_gradient)
                scaled_pixmap = pixmap.scaled(300, 300, Qt.KeepAspectRatio, Qt.SmoothTransformation)
                gradient_display.setPixmap(scaled_pixmap)
                
                gradient_layout.addWidget(gradient_display)
        
        # 将模态标签页添加到ROI布局中
        if modality_tab.layout().count() > 0:
            roi_layout.addWidget(modality_tab)
    
    def on_processing_complete(self):
        """处理完成时的回调"""
        # 启用处理按钮
        self.process_btn.setEnabled(True)
        
        # 更新状态栏
        self.statusBar().showMessage("处理完成")


if __name__ == "__main__":
    app = QApplication(sys.argv)
    window = DetectionSystemApp()
    window.show()
    sys.exit(app.exec_())
