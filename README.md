# 多模态识别系统

一个基于PyQt的多模态图像识别系统，支持可见光、红外、SAR图像的自动处理和识别。

## 功能特点

### 🔍 智能数据类型识别
- 自动识别可见光、红外、SAR图像/视频
- 基于文件名和图像内容的双重判断
- 支持多种探测体制的自动检测

### 🎯 多域特征提取
- **空域特征**: 亮度、对比度、纹理、边缘密度等
- **频域特征**: 傅里叶变换、频谱分析、能量分布
- **小波域特征**: 小波变换、多尺度分析、系数统计

### 🌅 智能场景识别
- 基于可见光和红外特征差异判断白天/黑夜场景
- 提供详细的判断依据和置信度
- 自动生成场景识别报告

### 🎛️ 探测体制管理
- 单探测体制识别
- 特征融合识别策略
- 决策融合识别策略
- 根据场景自动调整权重分配

### 🎯 YOLO目标检测
- 支持多种YOLO模型
- 锚框偏移和置信度调整
- 策略调整前后对比显示
- 多体制检测结果融合

### 🔥 红外梯度特征
- 锚框区域梯度特征提取
- 梯度幅值和方向分析
- 可视化显示梯度特征
- 统计特征计算

### ⚡ 性能优化
- 多线程处理优化
- 实时数据保存
- 自动流程处理（无需手动按钮操作）
- 过程数据完整记录

## 系统架构

```
多模态识别系统
├── 数据输入层
│   ├── 文件选择器
│   └── 数据类型检测器
├── 特征提取层
│   ├── 空域特征提取器
│   ├── 频域特征提取器
│   └── 小波域特征提取器
├── 场景识别层
│   └── 场景识别器
├── 检测识别层
│   ├── 探测体制管理器
│   ├── YOLO检测处理器
│   └── 梯度特征提取器
├── 可视化层
│   ├── 图像显示组件
│   ├── 特征可视化组件
│   └── 梯度可视化组件
└── 数据管理层
    ├── 过程数据管理器
    └── 多线程处理器
```

## 安装要求

### 系统要求
- Python 3.9+
- Windows/Linux/macOS

### 依赖包
```bash
pip install -r requirements.txt
```

主要依赖：
- PyQt5 >= 5.15.10
- OpenCV >= 4.11.0
- NumPy >= 2.0.2
- SciPy >= 1.13.1
- scikit-image >= 0.24.0
- PyWavelets >= 1.4.1
- ultralytics >= 8.3.133
- matplotlib >= 3.9.4

## 使用方法

### 启动系统
```bash
python run_multimodal_system.py
```

### 操作流程
1. **选择文件**: 点击"选择图像/视频文件"按钮
2. **自动处理**: 系统自动执行以下步骤：
   - 数据类型检测
   - 多域特征提取
   - 场景识别
   - 探测体制策略确定
   - YOLO目标检测
   - 梯度特征提取
   - 结果可视化
3. **查看结果**: 在不同标签页查看处理结果

### 支持的文件格式
- **图像**: PNG, JPG, JPEG, BMP, TIF, TIFF
- **视频**: MP4, AVI, MOV, MKV

## 输出结果

### 处理过程数据
- 保存在 `process_data/session_YYYYMMDD_HHMMSS/` 目录
- 包含完整的处理日志和中间结果
- JSON格式的结构化数据

### 可视化结果
- 特征提取可视化图表
- 目标检测结果标注
- 梯度特征热力图
- 场景识别判断依据

## 核心算法

### 场景识别算法
```python
# 基于可见光和红外特征差异
if visible_mean > 100 and visible_contrast > 20:
    scene = "day"  # 白天场景
elif visible_mean < 80 and thermal_contrast > visible_contrast:
    scene = "night"  # 夜晚场景
```

### 权重分配策略
```python
# 根据场景调整探测体制权重
if scene == "day":
    visible_weight *= 1.2  # 白天可见光权重增加
    thermal_weight *= 0.8  # 白天红外权重降低
elif scene == "night":
    visible_weight *= 0.6  # 夜晚可见光权重降低
    thermal_weight *= 1.3  # 夜晚红外权重增加
```

## 技术特色

### 无按钮自动化
- 选择文件后自动开始处理
- 无需手动点击处理按钮
- 流程化的自动处理体验

### 实时进度显示
- 详细的处理步骤显示
- 实时进度条更新
- 完整的处理日志

### 多线程优化
- 后台处理不阻塞界面
- 实时数据保存
- 异常处理和错误恢复

## 扩展性

系统采用模块化设计，易于扩展：
- 新增探测体制支持
- 自定义特征提取算法
- 集成其他深度学习模型
- 添加新的可视化组件

## 许可证

本项目采用MIT许可证。

## 联系方式

如有问题或建议，请联系开发团队。
