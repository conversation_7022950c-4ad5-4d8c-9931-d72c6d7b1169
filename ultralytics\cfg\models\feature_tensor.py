import torch
import torch.nn as nn
import torch.nn.functional as F
import cv2
import numpy as np
import matplotlib.pyplot as plt


class TensorFeatureExtractor(nn.Module):
    def __init__(self, in_channels=64, out_channels=256):
        super().__init__()
        # 均值卷积
        self.mean_conv = nn.Conv2d(in_channels, in_channels, 3, padding=1, groups=in_channels, bias=False)
        with torch.no_grad():
            self.mean_conv.weight.fill_(1/9.0)
        # 通道调整
        self.final_conv = nn.Conv2d(in_channels, out_channels, 3, padding=1)

    def forward(self, x):
        x = self.mean_conv(x)
        # Sobel边缘特征
        ch = x.shape[1]
        sobel_x = torch.tensor([[1,0,-1],[2,0,-2],[1,0,-1]], dtype=torch.float32, device=x.device).reshape(1,1,3,3).repeat(ch,1,1,1)
        sobel_y = torch.tensor([[1,2,1],[0,0,0],[-1,-2,-1]], dtype=torch.float32, device=x.device).reshape(1,1,3,3).repeat(ch,1,1,1)
        edge_x = F.conv2d(x, sobel_x, padding=1, groups=ch)
        edge_y = F.conv2d(x, sobel_y, padding=1, groups=ch)
        edge = torch.sqrt(edge_x**2 + edge_y**2)
        x = x + 0.8*edge
        x = self.final_conv(x)
        return x